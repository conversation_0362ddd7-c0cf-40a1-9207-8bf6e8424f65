# -*- mode: python ; coding: utf-8 -*-

import face_recognition_models
import os

# Get the path to face recognition models
models_path = os.path.dirname(face_recognition_models.__file__)

a = Analysis(
    ['social_search_result.py'],
    pathex=[],
    binaries=[],
    datas=[
        (os.path.join(models_path, 'models'), 'face_recognition_models/models'),
    ],
    hiddenimports=['face_recognition_models'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='social_search_result',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
