@echo off
setlocal enabledelayedexpansion

echo ===================================
echo Social Search Runner
echo ===================================
echo.

:: List social search scripts
echo Available Social Search scripts:
echo.
set index=1
set found=0

for %%f in (*.exe) do (
    echo !index!. %%f
    set "file!index!=%%f"
    set /a index+=1
    set found=1
)

if %found% EQU 0 (
    echo No social search scripts found in the current directory.
    echo.
    pause
    exit /b
)

echo.
set /p choice=Enter the number of the script to run (or 'q' to quit): 

if /i "%choice%"=="q" exit /b

:: Validate that input is a number
echo %choice%|findstr /R "^[0-9][0-9]*$" >nul
if errorlevel 1 (
    echo Invalid selection. Please enter a number from the list.
    pause
    exit /b
)

set /a choice_num=%choice%

set /a max_index=%index%-1
if !choice_num! LSS 1 (
    echo Invalid selection. Please enter a number from the list.
    pause
    exit /b
)
if !choice_num! GTR !max_index! (
    echo Invalid selection. Please enter a number from the list.
    pause
    exit /b
)

:: Run the selected Python script
set "script=!file%choice_num%!"
echo.
echo Running: !script!
echo ===================================
echo.
python "!script!"
echo.
echo ===================================
echo Script execution completed.
echo.
pause
