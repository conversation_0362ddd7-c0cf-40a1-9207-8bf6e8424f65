# -*- coding: utf-8 -*-
"""
Socialsearch Pro - Enhanced Enterprise OSINT Solution
Zero-Trust Architecture • Async-First Design • Military-Grade Security
Facial Recognition Enhanced with Profile Picture Comparison
"""
import asyncio
import aiofiles
import base64
import hashlib
import hmac
import json
import logging
import os
import secrets
import time
import io
from datetime import datetime, timed<PERSON>ta
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union
from urllib.parse import urlparse
import httpx
import magic
import structlog
from cryptography.fernet import Fernet
from pydantic import (
    BaseModel,
    Field,
    SecretStr,
    field_validator,
    ConfigDict,
    HttpUrl,
    model_validator,
)
from tenacity import (
    AsyncRetrying,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
)
# Optional monitoring dependencies
try:
    from async_lru import alru_cache
except ImportError:
    # Fallback: simple cache decorator without LRU functionality
    def alru_cache(maxsize=None, ttl=None):
        def decorator(func):
            cache = {}
            async def wrapper(*args, **kwargs):
                key = str(args) + str(sorted(kwargs.items()))
                if key not in cache:
                    cache[key] = await func(*args, **kwargs)
                return cache[key]
            return wrapper
        return decorator
try:
    from prometheus_client import Counter, Histogram, start_http_server
except ImportError:
    # Fallback: dummy metrics classes
    class Counter:
        def __init__(self, *args, **kwargs):
            pass
        def labels(self, **kwargs):
            return self
        def inc(self, amount=1):
            pass
    class Histogram:
        def __init__(self, *args, **kwargs):
            pass
        def labels(self, **kwargs):
            return self
        def observe(self, amount):
            pass
        def time(self):
            return self
        def __enter__(self):
            return self
        def __exit__(self, *args):
            pass
    def start_http_server(port):
        pass
# Optional dependencies
try:
    import face_recognition
    from PIL import Image, ImageFilter
    import cv2
    import numpy as np
    FACE_RECOGNITION_AVAILABLE = True
except ImportError:
    FACE_RECOGNITION_AVAILABLE = False
# ====================== Metrics Setup ======================
REQUEST_COUNT = Counter(
    'socialsearch_requests_total',
    'Total API requests made',
    ['platform', 'status']
)
REQUEST_LATENCY = Histogram(
    'socialsearch_request_latency_seconds',
    'Request latency in seconds',
    ['platform']
)
IMAGE_ANALYSIS_COUNT = Counter(
    'socialsearch_image_analysis_total',
    'Total image analyses performed',
    ['status']
)
# ====================== Error Classes ======================
class SocialSearchError(Exception):
    """Base exception class for all application errors"""
    pass
class PlatformIntegrationError(SocialSearchError):
    """Platform-specific API errors"""
    def __init__(self, platform: str, message: str):
        self.platform = platform
        super().__init__(f"{platform} error: {message}")
class ImageAnalysisError(SocialSearchError):
    """Image processing errors"""
    pass
class ConfigurationError(SocialSearchError):
    """Configuration validation errors"""
    pass
class SecurityError(SocialSearchError):
    """Security-related errors"""
    pass
# ====================== Logging Configuration ======================
def configure_logging(debug: bool = False):
    """Configure structured logging with appropriate processors"""
    processors = [
        structlog.contextvars.merge_contextvars,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
    ]
    if debug:
        processors.append(structlog.dev.ConsoleRenderer())
    else:
        processors.append(structlog.processors.JSONRenderer())
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=structlog.stdlib.LoggerFactory(),
        cache_logger_on_first_use=True,
    )
    logger = structlog.get_logger(__name__)
    return logger
logger = configure_logging()
# ====================== Configuration Models ======================
class SecurityConfig(BaseModel):
    """Security-related configuration"""
    model_config = ConfigDict(extra='forbid')
    encryption_key: SecretStr = Field(..., min_length=32)
    key_rotation_days: int = Field(7, ge=0)
    sanitize_inputs: bool = True
    request_signatures: bool = True
    audit_logging: bool = True
    max_retries: int = Field(3, ge=0, le=10)
    max_file_size: int = Field(10_485_760, ge=1_048_576)  # 10MB minimum
    hmac_signing: bool = True
    request_timeout: int = Field(30, ge=5, le=120)  # seconds
    @field_validator("encryption_key")
    @classmethod
    def validate_key_length(cls, v: SecretStr) -> SecretStr:
        try:
            key_bytes = base64.urlsafe_b64decode(v.get_secret_value().encode())
            if len(key_bytes) != 32:
                raise ValueError("Encryption key must decode to exactly 32 bytes for Fernet.")
        except (TypeError, ValueError, base64.binascii.Error) as e:
            raise ValueError(f"Invalid encryption key: {e}")
        return v
    def rotate_key(self) -> SecretStr:
        """Generates and sets a new encryption key."""
        new_raw_key = secrets.token_bytes(32)
        new_key = base64.urlsafe_b64encode(new_raw_key).decode()
        self.encryption_key = SecretStr(new_key)
        logger.info("Security key rotated successfully")
        return self.encryption_key
class ImageAnalysisConfig(BaseModel):
    """Image analysis configuration"""
    model_config = ConfigDict(extra='forbid')
    enable_facial_recognition: bool = Field(True)
    face_detection_model: str = Field("hog")  # or "cnn"
    min_confidence_threshold: float = Field(0.6, ge=0.0, le=1.0)
    blur_threshold: float = Field(60.0, ge=0.0)  # Laplacian variance threshold
    min_face_size: int = Field(100, ge=20)  # minimum pixels for reliable detection
    @field_validator("face_detection_model")
    @classmethod
    def validate_model(cls, v):
        if v not in ["hog", "cnn"]:
            raise ValueError("face_detection_model must be 'hog' or 'cnn'")
        return v
class PlatformConfig(BaseModel):
    """Platform-specific configuration"""
    model_config = ConfigDict(extra='forbid')
    enabled: bool = False
    api_endpoint: Optional[HttpUrl] = None
    api_type: Optional[str] = None  # e.g., 'rapidapi', 'official_api'
    rapidapi_host: Optional[str] = None
    rapidapi_key: Optional[SecretStr] = None
    default_region: Optional[str] = None
    default_count: Optional[int] = Field(None, ge=1)
    max_requests_per_minute: int = Field(60, ge=1)
    retry_config: Dict[str, Any] = Field(
        default_factory=lambda: {
            "max_retries": 3,
            "base_delay": 1,
            "max_delay": 10
        }
    )
    @field_validator('rapidapi_key', mode='before')
    @classmethod
    def validate_rapidapi_key(cls, v):
        if isinstance(v, str) and v.strip() == "YOUR_RAPIDAPI_KEY_HERE":
            raise ValueError("Please replace the placeholder RapidAPI key")
        return SecretStr(v) if not isinstance(v, SecretStr) else v
    @model_validator(mode='after')
    def check_required_for_enabled_rapidapi(self):
        if self.enabled and self.api_type == 'rapidapi':
            if not all([self.rapidapi_host, self.rapidapi_key, self.api_endpoint]):
                raise ValueError("All RapidAPI fields are required when platform is enabled")
        return self
class DatabaseConfig(BaseModel):
    """Database configuration"""
    model_config = ConfigDict(extra='forbid')
    dsn: str
    max_connections: int = Field(10, ge=1)
    min_connections: int = Field(2, ge=1)
    connect_timeout: int = Field(10, ge=1)  # seconds
class SearchConfig(BaseModel):
    """Search configuration"""
    model_config = ConfigDict(extra='forbid')
    default_name: str = Field("John Doe", min_length=1)
    photo_path: Optional[str] = None
    platforms_to_search: List[str] = Field(default_factory=list)
    max_results_per_platform: int = Field(20, ge=1)
    min_name_length: int = Field(2, ge=1)
    @field_validator('platforms_to_search')
    @classmethod
    def convert_to_lower(cls, v):
        return [p.lower() for p in v]
class SocialMapperConfig(BaseModel):
    """Root configuration model"""
    model_config = ConfigDict(extra='forbid')
    security: SecurityConfig
    database: DatabaseConfig
    platforms: Dict[str, PlatformConfig]  # Using string keys for flexibility
    image_analysis: ImageAnalysisConfig = Field(default_factory=ImageAnalysisConfig)
    search: SearchConfig = Field(default_factory=SearchConfig)
    metrics_port: int = Field(9090, ge=1024, le=65535)
    debug_mode: bool = False
    @model_validator(mode='after')
    def validate_at_least_one_platform_enabled(self):
        if not any(p.enabled for p in self.platforms.values()):
            logger.warning("No platforms are enabled in configuration")
        return self
# ====================== Platform Enumeration ======================
class PlatformType(str, Enum):
    """Supported platform types"""
    FACEBOOK = "facebook"
    LINKEDIN = "linkedin"
    INSTAGRAM = "instagram"
    SNAPCHAT = "snapchat"
    TIKTOK = "tiktok"
    TWITTER = "twitter"
    YOUTUBE = "youtube"
# ====================== Core Components ======================
class NetworkManager:
    """Handles asynchronous HTTP requests with resilience patterns"""
    def __init__(self, security: SecurityConfig):
        self.security = security
        self.client = httpx.AsyncClient(
            limits=httpx.Limits(
                max_connections=200,
                max_keepalive_connections=50
            ),
            timeout=httpx.Timeout(self.security.request_timeout)
        )
        self.circuit_state = "closed"
        self.failure_count = 0
        self.circuit_open_until: Optional[datetime] = None
        self.failure_threshold = 5
        self.open_state_duration = timedelta(seconds=60)
        self.rate_limiter = asyncio.Semaphore(100)
        self.request_metrics = {
            "total": 0,
            "success": 0,
            "failures": 0,
            "latency": []
        }
    def _sign_request(self, method: str, url: str, headers: Dict, body: bytes = b"") -> Dict:
        """Adds HMAC signature to request headers"""
        if not self.security.request_signatures:
            return headers
        timestamp = datetime.now().isoformat()
        nonce = secrets.token_hex(8)
        parsed_url = urlparse(url)
        path = parsed_url.path + (f"?{parsed_url.query}" if parsed_url.query else "")
        # Create signature payload
        payload = f"{method}\n{path}\n{timestamp}\n{nonce}\n".encode()
        if body:
            # Convert body to bytes if it's not already
            if isinstance(body, dict):
                body_bytes = json.dumps(body, sort_keys=True).encode()
            elif isinstance(body, str):
                body_bytes = body.encode()
            elif isinstance(body, bytes):
                body_bytes = body
            else:
                body_bytes = str(body).encode()
            payload += hashlib.sha256(body_bytes).digest()
        # Sign with HMAC
        if self.security.hmac_signing:
            signature = hmac.new(
                self.security.encryption_key.get_secret_value().encode(),
                payload,
                hashlib.sha256
            ).hexdigest()
        else:
            # Fallback to simple hash (less secure)
            signature = hashlib.sha256(payload).hexdigest()
        headers.update({
            "X-Request-Signature": signature,
            "X-Timestamp": timestamp,
            "X-Nonce": nonce,
            "X-Path": path
        })
        return headers
    async def secure_request(
        self,
        method: str,
        url: str,
        platform: Optional[str] = None,
        **kwargs
    ) -> httpx.Response:
        """
        Performs a signed, rate-limited, retried HTTP request.
        Handles circuit breaking, retries, and metrics collection.
        """
        self.request_metrics["total"] += 1
        request_log = logger.bind(
            method=method,
            url=url,
            platform=platform
        )
        start_time = time.time()
        # Circuit breaker check
        if self.circuit_state == "open":
            if self.circuit_open_until and datetime.now() < self.circuit_open_until:
                request_log.warning("Request blocked by OPEN circuit breaker")
                REQUEST_COUNT.labels(platform=platform, status='circuit_breaker').inc()
                raise httpx.HTTPStatusError(
                    "Circuit breaker open",
                    request=httpx.Request(method, url),
                    response=httpx.Response(503, text="Service Unavailable")
                )
            else:
                request_log.info("Circuit breaker transitioning to HALF-OPEN")
                self.circuit_state = "half-open"
                self.failure_count = 0
        async with self.rate_limiter:
            try:
                # Get platform-specific retry config
                retry_config = kwargs.pop('retry_config', {
                    "max_retries": self.security.max_retries,
                    "base_delay": 1,
                    "max_delay": 10
                })
                async for attempt in AsyncRetrying(
                    stop=stop_after_attempt(retry_config["max_retries"] + 1),
                    wait=wait_exponential(
                        multiplier=1,
                        min=retry_config["base_delay"],
                        max=retry_config["max_delay"]
                    ),
                    retry=retry_if_exception_type(
                        (httpx.NetworkError, httpx.TimeoutException, httpx.ConnectError)
                    ),
                    reraise=True
                ):
                    with attempt:
                        # Prepare request
                        headers = self._sign_request(
                            method,
                            url,
                            kwargs.pop("headers", {}),
                            kwargs.get("data") or kwargs.get("json", b"")
                        )
                        request_log.debug(
                            "Sending request",
                            attempt=attempt.retry_state.attempt_number
                        )
                        response = await self.client.request(
                            method,
                            url,
                            headers=headers,
                            **kwargs
                        )
                        request_log = request_log.bind(
                            status_code=response.status_code,
                            attempt=attempt.retry_state.attempt_number
                        )
                        # Check for retryable status codes
                        if response.status_code >= 500 or response.status_code == 429:
                            request_log.warning(
                                "Server error or rate limit encountered",
                                status_code=response.status_code
                            )
                            response.raise_for_status()
                        response.raise_for_status()
                        self._record_success(start_time)
                        REQUEST_COUNT.labels(platform=platform, status='success').inc()
                        REQUEST_LATENCY.labels(platform=platform).observe(time.time() - start_time)
                        request_log.info("Request successful")
                        return response
            except httpx.HTTPStatusError as http_err:
                self._record_failure(start_time)
                REQUEST_COUNT.labels(platform=platform, status='http_error').inc()
                request_log.error(
                    "HTTP status error",
                    status_code=http_err.response.status_code,
                    error=str(http_err)
                )
                raise PlatformIntegrationError(
                    platform or "unknown",
                    f"HTTP error {http_err.response.status_code}: {http_err}"
                ) from http_err
            except httpx.RequestError as req_err:
                self._record_failure(start_time)
                REQUEST_COUNT.labels(platform=platform, status='network_error').inc()
                request_log.error("Request failed", error=str(req_err))
                raise PlatformIntegrationError(
                    platform or "unknown",
                    f"Network error: {req_err}"
                ) from req_err
            except Exception as exc:
                self._record_failure(start_time)
                REQUEST_COUNT.labels(platform=platform, status='error').inc()
                request_log.error("Unexpected error", exc_info=True)
                raise PlatformIntegrationError(
                    platform or "unknown",
                    f"Unexpected error: {exc}"
                ) from exc
    def _record_success(self, start_time: float) -> None:
        """Records a successful request"""
        latency = time.time() - start_time
        self.request_metrics["success"] += 1
        self.request_metrics["latency"].append(latency)
        if self.circuit_state == "half-open":
            logger.info("Circuit breaker reset to CLOSED after successful request")
            self.circuit_state = "closed"
            self.failure_count = 0
            self.circuit_open_until = None
        elif self.failure_count > 0:
            self.failure_count = max(0, self.failure_count - 1)
    def _record_failure(self, start_time: float) -> None:
        """Records a failed request and potentially trips circuit breaker"""
        latency = time.time() - start_time
        self.request_metrics["failures"] += 1
        self.request_metrics["latency"].append(latency)
        self.failure_count += 1
        if (self.circuit_state == "half-open" or 
            (self.circuit_state == "closed" and self.failure_count >= self.failure_threshold)):
            logger.warning(
                "Circuit breaker tripped to OPEN state",
                failure_count=self.failure_count
            )
            self.circuit_state = "open"
            self.circuit_open_until = datetime.now() + self.open_state_duration
    async def close(self):
        """Closes the HTTP client"""
        await self.client.aclose()
        logger.info("NetworkManager closed")
# ====================== Platform Integrators ======================
class PlatformIntegrator:
    """Base class for platform-specific search logic"""
    def __init__(self, config: Dict, network: NetworkManager):
        self.platform_name = self.__class__.__name__.lower().replace('integrator', '')
        self.config = config
        self.network = network
        self.rate_limiter = asyncio.Semaphore(
            self.config.get("max_requests_per_minute", 60) // 60 or 1
        )
        logger.debug(
            f"Initialized {self.platform_name} integrator",
            config={k: v for k, v in config.items() if not k.endswith('_key')}
        )
    async def search(self, name: str) -> Dict:
        """Performs a search for a given name on the platform"""
        raise NotImplementedError("Subclasses must implement search")
class FacebookIntegrator(PlatformIntegrator):
    """Facebook platform integrator"""
    async def search(self, name: str) -> Dict:
        logger.warning("Facebook search not implemented")
        return {
            "matches": [],
            "error": "Facebook API integration not implemented"
        }
class InstagramIntegrator(PlatformIntegrator):
    """Instagram platform integrator using RapidAPI"""
    @alru_cache(maxsize=100, ttl=3600)
    async def search(self, name: str) -> Dict:
        """Search Instagram with caching"""
        log = logger.bind(platform=self.platform_name, search_term=name)
        log.info("Starting Instagram search")
        if not all([self.config.get('api_endpoint'), 
                   self.config.get('rapidapi_host'),
                   self.config.get('rapidapi_key')]):
            error_msg = "Instagram API configuration incomplete"
            log.error(error_msg)
            return {"matches": [], "error": error_msg}
        rapidapi_key = self.config['rapidapi_key'].get_secret_value()
        if rapidapi_key == "YOUR_RAPIDAPI_KEY_HERE":
            error_msg = "Invalid RapidAPI key placeholder"
            log.error(error_msg)
            return {"matches": [], "error": error_msg}
        headers = {
            'x-rapidapi-host': self.config['rapidapi_host'],
            'x-rapidapi-key': rapidapi_key,
            'Content-Type': 'application/json'
        }
        payload = {'query': name}
        try:
            async with self.rate_limiter:
                response = await self.network.secure_request(
                    "POST",
                    str(self.config['api_endpoint']),
                    headers=headers,
                    json=payload,
                    platform=self.platform_name,
                    retry_config=self.config.get('retry_config')
                )
                user_data = response.json()
                log.debug("API response", response=user_data)
                matches = []
                potential_users = user_data.get('response', {}).get('body', {}).get('users', [])
                if not potential_users:
                    log.info("No users found for query")
                    return {"matches": [], "error": "No users found"}
                for user_entry in potential_users:
                    user_info = user_entry.get('user', {})
                    if not (username := user_info.get('username')):
                        continue
                    # Apply relevance filters
                    if not self._is_relevant_match(name, user_info):
                        continue
                    matches.append({
                        "id": str(user_info.get('pk', username)),
                        "name": user_info.get('full_name', username),
                        "username": username,
                        "platform": self.platform_name,
                        "profile_url": f"https://www.instagram.com/{username}",
                        "is_verified": user_info.get('is_verified', False),
                        "followers": user_info.get('follower_count', 0),
                        "profile_pic_url": user_info.get('profile_pic_url')
                    })
                log.info("Search completed", match_count=len(matches))
                return {"matches": matches}
        except Exception as e:
            log.error("Search failed", exc_info=True)
            raise PlatformIntegrationError(
                self.platform_name,
                f"Instagram search failed: {e}"
            ) from e
    def _is_relevant_match(self, search_name: str, user_info: Dict) -> bool:
        """Determine if a user is a relevant match"""
        search_lower = search_name.lower()
        full_name = user_info.get('full_name', '').lower()
        username = user_info.get('username', '').lower()
        # Name must appear in either full name or username
        if search_lower not in full_name and search_lower not in username:
            return False
        # Verified accounts always pass
        if user_info.get('is_verified', False):
            return True
        # Minimum follower threshold
        if user_info.get('follower_count', 0) < 1000:
            return False
        return True
# ====================== Image Analysis ======================
class ImageAnalysisEngine:
    """Handles image processing and facial recognition with enhanced accuracy."""
    def __init__(self, security: SecurityConfig, analysis_config: ImageAnalysisConfig):
        self.security = security
        self.analysis_config = analysis_config
        self.network = NetworkManager(security)
        self.fernet: Optional[Fernet] = None
        self._init_fernet()
        try:
            self.mime = magic.Magic(mime=True)
        except Exception as e:
            logger.error("Failed to initialize python-magic", exc_info=True)
            raise ImageAnalysisError("Image type detection unavailable") from e
        self._check_dependencies()

    def _init_fernet(self):
        """Initializes Fernet encryption"""
        try:
            key_bytes = base64.urlsafe_b64decode(
                self.security.encryption_key.get_secret_value().encode()
            )
            if len(key_bytes) != 32:
                raise SecurityError("Invalid Fernet key length")
            self.fernet = Fernet(self.security.encryption_key.get_secret_value().encode())
        except Exception as e:
            logger.error("Fernet initialization failed", exc_info=True)
            raise SecurityError(f"Encryption setup failed: {e}") from e

    def _check_dependencies(self):
        """Verifies required dependencies are available"""
        if (self.analysis_config.enable_facial_recognition and 
            not FACE_RECOGNITION_AVAILABLE):
            logger.error("Facial recognition dependencies missing")
            self.analysis_config.enable_facial_recognition = False
            raise ImageAnalysisError("Facial recognition dependencies not installed")

    async def analyze(self, photo_path: Path, profile_pics: List[Dict] = None) -> Dict:
        """
        Analyzes an image with enhanced facial recognition and compares to profile pictures.
        Returns analysis results including face matches.
        """
        log = logger.bind(photo_path=str(photo_path))
        IMAGE_ANALYSIS_COUNT.labels(status='started').inc()
        result = {
            "status": "pending",
            "file_path": str(photo_path),
            "file_type": None,
            "size_bytes": None,
            "face_count": 0,
            "profile_pic_matches": [],
            "error": None
        }
        try:
            # Validate image file
            if not await asyncio.to_thread(photo_path.is_file):
                raise FileNotFoundError(f"Image not found: {photo_path}")
            stat = await asyncio.to_thread(photo_path.stat)
            result["size_bytes"] = stat.st_size
            if result["size_bytes"] > self.security.max_file_size:
                raise ValueError(
                    f"File size {result['size_bytes']} exceeds limit "
                    f"{self.security.max_file_size}"
                )
            # Check MIME type
            result["file_type"] = await asyncio.to_thread(
                self.mime.from_file, str(photo_path)
            )
            if not result["file_type"].startswith("image/"):
                raise ValueError(f"Invalid file type: {result['file_type']}")

            # Load and validate image
            async with aiofiles.open(photo_path, "rb") as f:
                image_bytes = await f.read()
            img = await asyncio.to_thread(Image.open, io.BytesIO(image_bytes))

            # Quality checks
            if (img.width < self.analysis_config.min_face_size or
                img.height < self.analysis_config.min_face_size):
                raise ImageAnalysisError(
                    f"Image too small (min {self.analysis_config.min_face_size}px)"
                )
            if await asyncio.to_thread(
                self._is_blurry, img, self.analysis_config.blur_threshold
            ):
                raise ImageAnalysisError("Image is too blurry for analysis")

            # Perform enhanced facial recognition if enabled
            if self.analysis_config.enable_facial_recognition:
                # Convert PIL Image to numpy array for OpenCV processing
                img_array = await asyncio.to_thread(np.array, img)
                # Perform enhanced face recognition
                face_data_list = await asyncio.to_thread(
                    self._perform_enhanced_face_recognition, img_array
                )
                result["face_count"] = len(face_data_list)
                if face_data_list and profile_pics:
                    # Extract encodings for comparison
                    input_encodings = [face_data['encodings'] for face_data in face_data_list]
                    result["profile_pic_matches"] = await self._compare_with_profile_pics(
                        input_encodings, profile_pics
                    )
            result["status"] = "analyzed"
            IMAGE_ANALYSIS_COUNT.labels(status='success').inc()
            return result
        except Exception as e:
            log.error("Image analysis failed", exc_info=True)
            result["status"] = "error"
            result["error"] = str(e)
            IMAGE_ANALYSIS_COUNT.labels(status='error').inc()
            return result

    def _is_blurry(self, image: Image.Image, threshold: float) -> bool:
        """Check if image is blurry using Laplacian variance"""
        try:
            gray = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2GRAY)
            variance = cv2.Laplacian(gray, cv2.CV_64F).var()
            return variance < threshold
        except Exception as e:
            logger.warning("Blur detection failed", error=str(e))
            return False

    def _preprocess_image(self, img_array: np.ndarray) -> np.ndarray:
        """Apply preprocessing like histogram equalization for lighting normalization."""
        try:
            # Convert to YUV color space
            img_yuv = cv2.cvtColor(img_array, cv2.COLOR_RGB2YUV)
            # Equalize the histogram of the Y channel (luminance)
            img_yuv[:, :, 0] = cv2.equalizeHist(img_yuv[:, :, 0])
            # Convert back to RGB
            img_output = cv2.cvtColor(img_yuv, cv2.COLOR_YUV2RGB)
            return img_output
        except Exception as e:
             logger.warning(f"Image preprocessing failed: {e}")
             return img_array # Return original if preprocessing fails

    def _perform_enhanced_face_recognition(self, img_array: np.ndarray) -> List[Dict]:
        """
        Detects faces, validates them, and generates robust encodings.
        Returns a list of dictionaries containing face data.
        """
        if not FACE_RECOGNITION_AVAILABLE:
            raise ImageAnalysisError("Face recognition not available")

        try:
            # Preprocess image for better consistency
            preprocessed_img_array = self._preprocess_image(img_array)

            # --- Face Detection ---
            # Use the model specified in config (cnn is more accurate but slower)
            face_locations = face_recognition.face_locations(
                preprocessed_img_array,
                model=self.analysis_config.face_detection_model
            )

            validated_face_locations = []
            for location in face_locations:
                 # Optional: Check landmarks for basic face structure validation
                 # This adds robustness by ensuring detected regions have facial features
                 landmarks = face_recognition.face_landmarks(preprocessed_img_array, face_locations=[location])
                 if landmarks and len(landmarks) > 0:
                    # Basic check: presence of key features (more checks can be added)
                    lm = landmarks[0]
                    if all(key in lm for key in ['left_eye', 'right_eye', 'nose_tip', 'chin']):
                        validated_face_locations.append(location)
                    else:
                        logger.debug("Face landmark check failed for a detected region.")
                 else:
                     logger.debug("No landmarks found for a detected face region.")

            if not validated_face_locations:
                 logger.info("No faces with valid landmarks found.")
                 return []

            # --- Encoding Generation ---
            face_data_list = []
            for location in validated_face_locations:
                # Original face encoding
                encodings_original = face_recognition.face_encodings(preprocessed_img_array, known_face_locations=[location])

                # --- Enhanced Encoding Strategy ---
                # Generate encodings from slightly augmented crops to improve robustness
                enhanced_encodings = []
                if encodings_original:
                    # Use the original encoding
                    enhanced_encodings.extend(encodings_original)

                    # Get the bounding box coordinates
                    top, right, bottom, left = location
                    height, width, _ = preprocessed_img_array.shape

                    # Define margins for cropping (e.g., 10% of face dimensions)
                    margin_x = max(1, int((right - left) * 0.1))
                    margin_y = max(1, int((bottom - top) * 0.1))

                    # Create slightly enlarged crop coordinates, ensuring bounds
                    crop_top = max(0, top - margin_y)
                    crop_bottom = min(height, bottom + margin_y)
                    crop_left = max(0, left - margin_x)
                    crop_right = min(width, right + margin_x)

                    # Crop the face region with margin
                    cropped_face = preprocessed_img_array[crop_top:crop_bottom, crop_left:crop_right]

                    # --- Generate encodings from augmented versions ---
                    if cropped_face.size > 0: # Check if crop is valid
                        # 1. Slightly resized version
                        try:
                            resized_face = cv2.resize(cropped_face, None, fx=0.95, fy=0.95, interpolation=cv2.INTER_AREA)
                            # Pad back to original crop size or adjust face location accordingly
                            # Simpler approach: pad resized image to match crop dimensions if needed
                            # Or, re-detect face in resized image (more complex)
                            # Here, we'll try encoding directly if resized image is large enough
                            if resized_face.shape[0] > 20 and resized_face.shape[1] > 20: # Minimum size check
                                # Re-detect face in resized crop for accurate encoding
                                resized_face_locations = face_recognition.face_locations(resized_face, model=self.analysis_config.face_detection_model)
                                if resized_face_locations:
                                    resized_encodings = face_recognition.face_encodings(resized_face, known_face_locations=resized_face_locations)
                                    if resized_encodings:
                                        enhanced_encodings.extend(resized_encodings)
                        except Exception as e:
                            logger.debug(f"Resized face encoding failed: {e}")

                        # 2. Slightly brightened/contrasted version (basic example)
                        try:
                            # Convert to float for processing
                            cropped_face_f = cropped_face.astype(np.float32)
                            # Adjust brightness and contrast (example values)
                            alpha = 1.1 # Contrast control (1.0-3.0)
                            beta = 10   # Brightness control (0-100)
                            adjusted_face = np.clip(alpha * cropped_face_f + beta, 0, 255).astype(np.uint8)
                            adjusted_face_locations = face_recognition.face_locations(adjusted_face, model=self.analysis_config.face_detection_model)
                            if adjusted_face_locations:
                                adjusted_encodings = face_recognition.face_encodings(adjusted_face, known_face_locations=adjusted_face_locations)
                                if adjusted_encodings:
                                    enhanced_encodings.extend(adjusted_encodings)
                        except Exception as e:
                            logger.debug(f"Adjusted face encoding failed: {e}")

                # Store face data
                if enhanced_encodings: # Only store if we have encodings
                    face_data_list.append({
                        'location': location,
                        'encodings': enhanced_encodings # Store list of encodings
                    })

            return face_data_list

        except Exception as e:
            logger.error("Enhanced face detection/encoding failed", exc_info=True)
            raise ImageAnalysisError(f"Enhanced face detection failed: {e}") from e


    async def _compare_with_profile_pics(
        self,
        input_face_data_list: List[List[np.ndarray]], # List of lists of encodings
        profile_pics: List[Dict]
    ) -> List[Dict]:
        """Compares input face encodings (with enhancements) with profile pictures."""
        matches = []
        # Flatten the input encodings for easier processing if needed,
        # or iterate per detected face in the input image.
        # Here, we'll iterate through each face found in the input image.
        # input_face_data_list is a list where each item represents data for one detected face,
        # and 'encodings' within that item is a list of encodings for that face.

        async def process_profile(profile: Dict):
            try:
                if not (pic_url := profile.get("profile_pic_url")):
                    return None
                response = await self.network.secure_request(
                    "GET", pic_url, platform="image_download"
                )
                pic_bytes = response.content
                # Convert profile pic bytes to array
                profile_img_array = face_recognition.load_image_file(io.BytesIO(pic_bytes))
                # Perform face recognition on profile pic
                profile_face_locations = face_recognition.face_locations(
                    profile_img_array,
                    model=self.analysis_config.face_detection_model # Use same model for consistency
                )
                if not profile_face_locations:
                    logger.debug(f"No face found in profile pic for {profile.get('username')}")
                    return None

                # Get encodings for profile pic face(s)
                profile_face_encodings = face_recognition.face_encodings(profile_img_array, profile_face_locations)

                if not profile_face_encodings:
                    logger.debug(f"No encodings generated for profile pic face for {profile.get('username')}")
                    return None

                local_matches = []
                # Compare each detected face in the input image
                for input_face_idx, input_face_data in enumerate(input_face_data_list):
                    input_encodings_for_face = input_face_data['encodings'] # This is a list

                    # For each input encoding of the current input face, compare against profile encodings
                    for input_encoding in input_encodings_for_face:
                         # --- Enhanced Matching Logic ---
                        # Calculate distances to all profile encodings found
                        distances = face_recognition.face_distance(profile_face_encodings, input_encoding)

                        if len(distances) > 0:
                            # Find the minimum distance for this specific input encoding
                            min_distance = np.min(distances)
                            # Check if the minimum distance is below the threshold
                            # Using squared distance or direct threshold comparison
                            # The default face_recognition threshold is around 0.6 for tolerance=0.6
                            # We can make this stricter or use the distance directly.
                            if min_distance <= (1.0 - self.analysis_config.min_confidence_threshold):
                                # Find the index of the best match
                                best_match_index = np.argmin(distances)
                                confidence = max(0.0, 1.0 - min_distance) # Convert distance to confidence-like score

                                # Only report if confidence is above the threshold
                                if confidence >= self.analysis_config.min_confidence_threshold:
                                    local_matches.append({
                                        "input_face_index": input_face_idx,
                                        "profile_face_index": int(best_match_index), # Ensure JSON serializable
                                        "username": profile.get("username"),
                                        "profile_url": profile.get("profile_url"),
                                        "confidence": float(confidence),
                                        "distance": float(min_distance), # Include distance for debugging/transparency
                                        "platform": profile.get("platform")
                                    })
                                else:
                                    logger.debug(f"Match found but confidence {confidence} below threshold {self.analysis_config.min_confidence_threshold} for {profile.get('username')}")

                return local_matches if local_matches else None
            except Exception as e:
                logger.warning(f"Profile pic processing failed for {profile.get('username', 'unknown')}: {e}")
                return None

        # Process profiles concurrently
        tasks = [process_profile(p) for p in profile_pics]
        results = await asyncio.gather(*tasks, return_exceptions=True) # Handle exceptions gracefully

        # Collect matches
        for result in results:
             if isinstance(result, Exception):
                 logger.error(f"Error during profile comparison: {result}")
                 continue
             if result: # If result is not None and not empty
                matches.extend(result)

        # Optional: Deduplicate matches if the same profile/user is matched multiple times
        # (e.g., if multiple faces in input match the same profile, or multiple encodings lead to same match)
        # This requires defining what constitutes a duplicate (same username/platform, high confidence overlap)
        # For simplicity, we'll return all found matches.

        return matches

# ====================== Main Application ======================
class SocialMapperPro:
    """Main application class orchestrating search and analysis"""
    def __init__(self, config: Dict[str, Any]):
        try:
            self.config = SocialMapperConfig(**config)
            logger.info("Configuration validated")
            # Initialize components
            self.security = self.config.security
            self.database = AsyncDatabase(self.config.database.dsn)
            self.network = NetworkManager(self.security)
            self.image_analyzer = ImageAnalysisEngine(
                self.security,
                self.config.image_analysis
            )
            # Initialize platform integrators
            self.integrators = self._init_platforms()
            self._key_rotation_task = None
            # Start metrics server
            if not self.config.debug_mode:
                start_http_server(self.config.metrics_port)
                logger.info(f"Metrics server started on port {self.config.metrics_port}")
            logger.info("SocialMapperPro initialized")
        except Exception as e:
            logger.critical("Initialization failed", exc_info=True)
            raise ConfigurationError(f"Configuration error: {e}") from e
    def _init_platforms(self) -> Dict[str, PlatformIntegrator]:
        """Initialize enabled platform integrators"""
        integrators = {}
        integrator_classes = {
            "facebook": FacebookIntegrator,
            "instagram": InstagramIntegrator,
            # Add other platforms...
        }
        for platform_name, platform_config in self.config.platforms.items():
            if platform_config.enabled and platform_name in integrator_classes:
                integrators[platform_name] = integrator_classes[platform_name](
                    platform_config.model_dump(),
                    self.network
                )
                logger.debug(f"Initialized {platform_name} integrator")
        return integrators
    async def initialize(self):
        """Initialize connections and background tasks"""
        await self.database.connect()
        self._schedule_key_rotation()
        if self.security.audit_logging:
            await self.log_audit_event("SystemInitialized", {"status": "success"})
        logger.info("Initialization complete")
    async def shutdown(self):
        """Shutdown connections and tasks"""
        if self._key_rotation_task:
            self._key_rotation_task.cancel()
            try:
                await self._key_rotation_task
            except asyncio.CancelledError:
                logger.info("Key rotation task cancelled")
        if self.security.audit_logging:
            await self.log_audit_event("SystemShutdown", {"status": "success"})
        await self.network.close()
        await self.database.disconnect()
        logger.info("Shutdown complete")
    async def full_analysis(self, name: str, photo_path: Optional[Path], platforms: List[str]) -> Dict:
        """
        Perform complete analysis across platforms with optional image comparison.
        Returns comprehensive results including matches and analysis data.
        """
        query_id = secrets.token_hex(8)
        log = logger.bind(query_id=query_id, name=name, platforms=platforms)
        start_time = time.time()
        results = {
            "query_id": query_id,
            "query_details": {
                "name": name,
                "photo_path": str(photo_path) if photo_path else None,
                "platforms_requested": platforms,
                "timestamp": datetime.now().isoformat()
            },
            "platform_results": {},
            "image_analysis_result": {},
            "summary": {
                "overall_status": "pending",
                "errors": []
            }
        }
        try:
            # Audit log start
            if self.security.audit_logging:
                await self.log_audit_event("AnalysisStarted", {
                    "query_id": query_id,
                    "name": name,
                    "platforms": platforms
                })
            # Validate inputs
            if len(name) < self.config.search.min_name_length:
                raise ValueError(
                    f"Name too short (min {self.config.search.min_name_length} chars)"
                )
            # Execute platform searches
            platform_tasks = []
            for platform in platforms:
                if platform in self.integrators:
                    platform_tasks.append(
                        (platform, asyncio.create_task(
                            self._search_platform(platform, name, query_id)
                        ))
                    )
                else:
                    error_msg = f"Platform {platform} not configured"
                    results["platform_results"][platform] = {
                        "status": "error",
                        "error": error_msg
                    }
                    results["summary"]["errors"].append(error_msg)
            # Gather platform results
            for platform, task in platform_tasks:
                try:
                    results["platform_results"][platform] = await task
                    if results["platform_results"][platform].get("error"):
                        results["summary"]["errors"].append(
                            f"{platform}: {results['platform_results'][platform]['error']}"
                        )
                except Exception as e:
                    error_msg = f"{platform} search failed: {str(e)}"
                    results["platform_results"][platform] = {
                        "status": "error",
                        "error": error_msg
                    }
                    results["summary"]["errors"].append(error_msg)
            # Perform image analysis if requested
            if photo_path and self.config.image_analysis.enable_facial_recognition:
                try:
                    results["image_analysis_result"] = await self.image_analyzer.analyze(
                        photo_path,
                        [
                            m for r in results["platform_results"].values()
                            for m in r.get("matches", [])
                            if m and m.get("profile_pic_url")
                        ]
                    )
                    if results["image_analysis_result"].get("error"):
                        results["summary"]["errors"].append(
                            f"Image analysis: {results['image_analysis_result']['error']}"
                        )
                except Exception as e:
                    error_msg = f"Image analysis failed: {str(e)}"
                    results["image_analysis_result"] = {
                        "status": "error",
                        "error": error_msg
                    }
                    results["summary"]["errors"].append(error_msg)
            # Generate summary
            results["summary"].update(self._generate_summary(results))
            results["duration_seconds"] = time.time() - start_time
            # Audit log completion
            if self.security.audit_logging:
                await self.log_audit_event("AnalysisCompleted", {
                    "query_id": query_id,
                    "duration": results["duration_seconds"],
                    "summary": results["summary"]
                })
            return results
        except Exception as e:
            error_msg = f"Analysis failed: {str(e)}"
            log.error(error_msg, exc_info=True)
            results["summary"]["errors"].append(error_msg)
            results["summary"]["overall_status"] = "failed"
            if self.security.audit_logging:
                await self.log_audit_event("AnalysisFailed", {
                    "query_id": query_id,
                    "error": error_msg
                })
            return results
    async def _search_platform(self, platform: str, name: str, query_id: str) -> Dict:
        """Execute search on a single platform"""
        log = logger.bind(
            query_id=query_id,
            platform=platform,
            search_term=name
        )
        try:
            if self.security.audit_logging:
                await self.log_audit_event("PlatformSearchStarted", {
                    "query_id": query_id,
                    "platform": platform,
                    "name": name
                })
            result = await self.integrators[platform].search(name)
            if self.security.audit_logging:
                await self.log_audit_event("PlatformSearchCompleted", {
                    "query_id": query_id,
                    "platform": platform,
                    "match_count": len(result.get("matches", [])),
                    "status": "success" if not result.get("error") else "error"
                })
            return {
                "status": "completed" if not result.get("error") else "error",
                "matches": result.get("matches", []),
                "error": result.get("error")
            }
        except Exception as e:
            log.error("Platform search failed", exc_info=True)
            if self.security.audit_logging:
                await self.log_audit_event("PlatformSearchFailed", {
                    "query_id": query_id,
                    "platform": platform,
                    "error": str(e)
                })
            return {
                "status": "error",
                "matches": [],
                "error": str(e)
            }
    def _generate_summary(self, results: Dict) -> Dict:
        """Generate analysis summary from results"""
        summary = {
            "platform_matches": sum(
                len(r.get("matches", []))
                for r in results["platform_results"].values()
                if r.get("status") == "completed"
            ),
            "face_matches": len(results.get("image_analysis_result", {}).get("profile_pic_matches", [])),
            "platform_statuses": {
                p: r.get("status", "unknown")
                for p, r in results["platform_results"].items()
            },
            "image_analysis_status": results.get("image_analysis_result", {}).get("status", "not_run")
        }
        # Determine overall status
        if any(r.get("status") == "error" for r in results["platform_results"].values()):
            summary["overall_status"] = "completed_with_errors"
        elif results.get("image_analysis_result", {}).get("status") == "error":
            summary["overall_status"] = "completed_with_errors"
        else:
            summary["overall_status"] = "completed"
        return summary
    async def log_audit_event(self, event: str, details: Dict):
        """Log audit event with sensitive data sanitization"""
        sanitized = self._sanitize_details(details)
        await self.database.log_audit(event, sanitized)
    def _sanitize_details(self, details: Any) -> Any:
        """Recursively sanitize sensitive data"""
        sensitive_keys = {'key', 'secret', 'token', 'password', 'signature'}
        if isinstance(details, dict):
            return {
                k: "***REDACTED***" if any(s in k.lower() for s in sensitive_keys) or isinstance(v, SecretStr)
                else self._sanitize_details(v)
                for k, v in details.items()
            }
        elif isinstance(details, list):
            return [self._sanitize_details(i) for i in details]
        elif isinstance(details, (str, int, float, bool)) or details is None:
            return details
        else:
            return str(details)
    def _schedule_key_rotation(self):
        """Schedule periodic security key rotation"""
        if self.security.key_rotation_days <= 0:
            return
        async def rotate_periodically():
            while True:
                try:
                    await asyncio.sleep(self.security.key_rotation_days * 86400)
                    new_key = self.security.rotate_key()
                    self.image_analyzer._init_fernet()  # Reinitialize with new key
                    if self.security.audit_logging:
                        await self.log_audit_event("KeyRotated", {
                            "rotation_interval_days": self.security.key_rotation_days
                        })
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    logger.error("Key rotation failed", exc_info=True)
                    await asyncio.sleep(3600)  # Retry in 1 hour on failure
        self._key_rotation_task = asyncio.create_task(rotate_periodically())
# ====================== Async Database ======================
class AsyncDatabase:
    """Simulated async database for audit logging"""
    def __init__(self, dsn: str):
        self.dsn = dsn
        self.connected = False
    async def connect(self):
        """Simulate database connection"""
        await asyncio.sleep(0.1)  # Simulate connection time
        self.connected = True
        logger.info("Database connected")
    async def disconnect(self):
        """Simulate disconnection"""
        if self.connected:
            await asyncio.sleep(0.05)
            self.connected = False
            logger.info("Database disconnected")
    async def log_audit(self, event: str, details: Dict):
        """Log audit event"""
        if not self.connected:
            logger.warning("Database not connected, skipping audit log")
            return
        try:
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "event": event,
                "details": details
            }
            await asyncio.sleep(0.01)  # Simulate write time
            logger.debug("Audit log simulated", entry=log_entry)
        except Exception as e:
            logger.error("Failed to write audit log", error=str(e))
# ====================== Main Function ======================
async def main():
    """Main entry point"""
    try:
        # Load configuration
        config_path = Path("config.json")
        if not config_path.exists():
            logger.error("Configuration file not found")
            return
        with open(config_path) as f:
            config = json.load(f)
        # Initialize and run application
        app = SocialMapperPro(config)
        try:
            await app.initialize()
            # Run analysis with configured parameters
            results = await app.full_analysis(
                name=app.config.search.default_name,
                photo_path=Path(app.config.search.photo_path) if app.config.search.photo_path else None,
                platforms=app.config.search.platforms_to_search
            )
            # Display results in terminal
            print("\n" + "="*80)
            print("🔍 SOCIAL SEARCH RESULTS".center(80))
            print("="*80)
            # Query Information
            print(f"\n📋 Query Details:")
            print(f"   • Query ID: {results['query_id']}")
            print(f"   • Search Term: {results['query_details']['name']}")
            print(f"   • Platforms: {', '.join(results['query_details']['platforms_requested'])}")
            print(f"   • Timestamp: {results['query_details']['timestamp']}")
            print(f"   • Duration: {results.get('duration_seconds', 0):.2f} seconds")
            # Platform Results
            print(f"\n🌐 Platform Search Results:")
            for platform, result in results['platform_results'].items():
                status_emoji = "✅" if result['status'] == 'completed' else "❌"
                print(f"   {status_emoji} {platform.upper()}:")
                if result['status'] == 'completed' and result['matches']:
                    print(f"      Found {len(result['matches'])} match(es):")
                    for i, match in enumerate(result['matches'], 1):
                        print(f"      {i}. {match.get('name', 'Unknown')}")
                        print(f"         • Username: @{match.get('username', 'N/A')}")
                        print(f"         • Verified: {'✅ Yes' if match.get('is_verified') else '❌ No'}")
                        print(f"         • Profile: {match.get('profile_url', 'N/A')}")
                        if match.get('followers') is not None:
                            print(f"         • Followers: {match.get('followers'):,}")
                        print()
                elif result['status'] == 'completed':
                    print(f"      No matches found")
                else:
                    print(f"      Error: {result.get('error', 'Unknown error')}")
                print()
            # Image Analysis Results
            if 'image_analysis_result' in results:
                img_result = results['image_analysis_result']
                print(f"📸 Image Analysis Results:")
                status_emoji = "✅" if img_result['status'] == 'analyzed' else "❌"
                print(f"   {status_emoji} Status: {img_result['status']}")
                if img_result['status'] == 'analyzed':
                    file_path = img_result.get('file_path', 'N/A')
                    file_name = Path(file_path).name if file_path != 'N/A' else 'N/A'
                    print(f"   • File: {file_name}")
                    print(f"   • Type: {img_result.get('file_type', 'N/A')}")
                    print(f"   • Size: {img_result.get('size_bytes', 0):,} bytes")
                    print(f"   • Faces Detected: {img_result.get('face_count', 0)}")
                    if img_result.get('profile_pic_matches'):
                        print(f"   • Face Matches Found: {len(img_result['profile_pic_matches'])}")
                        for match in img_result['profile_pic_matches']:
                            print(f"     - {match.get('username', 'Unknown')} (confidence: {match.get('confidence', 0):.2f})")
                    else:
                        print(f"   • Face Matches: None found")
                if img_result.get('error'):
                    print(f"   • Error: {img_result['error']}")
                print()
            # Summary
            summary = results.get('summary', {})
            print(f"📊 Summary:")
            status_emoji = "✅" if summary.get('overall_status') == 'completed' else "⚠️" if 'error' in summary.get('overall_status', '') else "❌"
            print(f"   {status_emoji} Overall Status: {summary.get('overall_status', 'Unknown')}")
            print(f"   • Platform Matches: {summary.get('platform_matches', 0)}")
            print(f"   • Face Matches: {summary.get('face_matches', 0)}")
            if summary.get('errors'):
                print(f"   • Errors: {len(summary['errors'])}")
                for error in summary['errors']:
                    print(f"     - {error}")
            else:
                print(f"   • Errors: None")
            print("\n" + "="*80)
            # Output results to file
            output_path = Path(f"results_{results['query_id']}.json")
            try:
                with open(output_path, "w") as f:
                    json.dump(results, f, indent=2)
                print(f"💾 Results saved to: {output_path}")
            except FileNotFoundError:
                # Try saving to the user's home directory as a fallback
                import os
                home_dir = os.path.expanduser("~")
                alt_output_path = os.path.join(home_dir, f"results_{results['query_id']}.json")
                with open(alt_output_path, "w") as f:
                    json.dump(results, f, indent=2)
                print(f"💾 Results saved to: {alt_output_path}")
            print("="*80 + "\n")
        finally:
            await app.shutdown()
    except Exception as e:
        logger.critical("Application failed", exc_info=True)
        raise
if __name__ == "__main__":
    asyncio.run(main())
