{
  "security": {
    "encryption_key": "your-secure-encryption-key-goes-here-min-32-chars",
    "key_rotation_days": 7,
    "sanitize_inputs": true,
    "request_signatures": true,
    "audit_logging": true,
    "max_retries": 3,
    "max_file_size": 10485760
  },
  "database": {
    "dsn": "postgresql+asyncpg://user:pass@localhost/db"
  },
  "platforms": {
    "tiktok": {
      "api_endpoint": "https://tiktok-api23.p.rapidapi.com",
      "api_type": "rapidapi",
      "rapidapi_host": "tiktok-api23.p.rapidapi.com",
      "rapidapi_key": "**************************************************",
      "default_count": 30,
      "default_secUid": "MS4wLjABAAAAqB08cUbXaDWqbD6MCga2RbGTuhfO2EsHayBYx08NDrN7IE3jQuRDNNN6YwyfH6_6"
    },
    "instagram": {
      "api_endpoint": "https://flashapi1.p.rapidapi.com",
      "api_type": "rapidapi",
      "rapidapi_host": "flashapi1.p.rapidapi.com",
      "rapidapi_key": "**************************************************"
    }
 "instagram": {
      "api_endpoint": "https://instagram-premium-api-2023.p.rapidapi.com/v2/userstream/by/username?username=viratkohli",
      "api_type": "rapidapi",
      "rapidapi_host": "x-rapidapi-host: instagram-premium-api-2023.p.rapidapi.com",
      "rapidapi_key": "**************************************************"
  },
  "search": {
    "default_name": "mistymeme",
    "photo_path": "profile.jpg",
    "platforms_to_search": ["instagram", "tiktok"]
  }
}


