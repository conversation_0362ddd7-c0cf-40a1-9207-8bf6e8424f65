{"query_id": "77121296d409ebe8", "query_details": {"name": "virat koh<PERSON>a", "photo_path": "C:\\Users\\<USER>\\Desktop\\Social search API\\profile1.jpg", "photo_provided": true, "platforms_requested": ["instagram"], "timestamp_utc": "2025-07-24T18:33:21.001781"}, "image_analysis_result": {"status": "analyzed", "file_path": "C:\\Users\\<USER>\\Desktop\\Social search API\\profile1.jpg", "file_type": "image/jpeg", "size_bytes": 754832, "error": null, "facial_recognition_enabled_in_config": true, "facial_recognition_actually_run": true, "face_count": 1, "profile_pic_matches": []}, "summary": {"overall_status": "completed", "errors_encountered": null, "total_platform_matches_found": 1, "platforms_summary": {"instagram": {"status": "completed", "match_count": 1, "error": null}}, "image_analysis_summary": {"status": "analyzed", "face_count": 1, "face_match_count": 0, "error": null}}, "analysis_duration_seconds": 25.746, "platform_results_sanitized": {"instagram": {"status": "completed", "error": null, "matches": [{"id": "2094200507", "name": "<PERSON><PERSON><PERSON>", "username": "virat.kohli", "platform": "instagram", "profile_url": "https://www.instagram.com/virat.kohli", "is_verified": true, "followers": 0, "bio": "", "profile_pic_url": "https://scontent-vie1-1.cdninstagram.com/v/t51.2885-19/331017149_3373809859551320_1963035851400324431_n.jpg?stp=dst-jpg_e0_s150x150_tt6&efg=eyJ2ZW5jb2RlX3RhZyI6InByb2ZpbGVfcGljLmRqYW5nby4xMDgwLmMyIn0&_nc_ht=scontent-vie1-1.cdninstagram.com&_nc_cat=1&_nc_oc=Q6cZ2QE96MjeIRYsNNy5Y8CWSGJry8Xm_3ixTj2if-j041U2qwxrIeXg_i0oYuj7elQ7jOs&_nc_ohc=1QjxBlILhVQQ7kNvwH31_Oi&_nc_gid=eGkQwKUsQbH7DOzCE-8mqg&edm=AHG7ALcBAAAA&ccb=7-5&ig_cache_key=GL3ruhNY4DdSdvwLAE_lEsy0Gj4bbkULAAAB-ccb7-5&oh=00_AfQKlOzzQMq5JS9GGpGKKWpHOM3ff67GFXtBkgJo06PkCQ&oe=68880456&_nc_sid=c9086e"}]}}}