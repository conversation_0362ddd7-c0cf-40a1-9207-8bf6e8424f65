{"security": {"encryption_key": "L2FlGQC3NXk1BEzYdqbuT-d8hVrzsAXKvhiy-2k3ddc=", "key_rotation_days": 7, "sanitize_inputs": true, "request_signatures": true, "audit_logging": true, "max_retries": 3, "max_file_size": 10485760, "hmac_signing": true, "request_timeout": 30}, "database": {"dsn": "postgresql+asyncpg://your_username:your_password@localhost:5432/socialsearchdb"}, "platforms": {"facebook": {"enabled": false}, "tiktok": {"enabled": false}, "instagram": {"enabled": true, "api_endpoint": "https://rocketapi-for-developers.p.rapidapi.com/instagram/search", "api_type": "rapidapi", "rapidapi_host": "rocketapi-for-developers.p.rapidapi.com", "rapidapi_key": "**************************************************", "default_count": 20}, "linkedin": {"enabled": false}, "snapchat": {"enabled": false}}, "image_analysis": {"enable_facial_recognition": true, "face_detection_model": "hog", "min_confidence_threshold": 0.6}, "search": {"default_name": "cristano", "photo_path": "profile.jpg", "platforms_to_search": ["instagram"]}}