('C:\\Users\\<USER>\\Desktop\\Social search '
 'API\\build\\social_search_result\\PYZ-00.pyz',
 [('PIL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('__future__', 'C:\\Python313\\Lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'C:\\Python313\\Lib\\_aix_support.py', 'PYMODULE'),
  ('_colorize', 'C:\\Python313\\Lib\\_colorize.py', 'PYMODULE'),
  ('_compat_pickle', 'C:\\Python313\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'C:\\Python313\\Lib\\_compression.py', 'PYMODULE'),
  ('_distutils_hack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_ios_support', 'C:\\Python313\\Lib\\_ios_support.py', 'PYMODULE'),
  ('_opcode_metadata', 'C:\\Python313\\Lib\\_opcode_metadata.py', 'PYMODULE'),
  ('_py_abc', 'C:\\Python313\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime', 'C:\\Python313\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', 'C:\\Python313\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyrepl', 'C:\\Python313\\Lib\\_pyrepl\\__init__.py', 'PYMODULE'),
  ('_pyrepl._minimal_curses',
   'C:\\Python313\\Lib\\_pyrepl\\_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl._threading_handler',
   'C:\\Python313\\Lib\\_pyrepl\\_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.commands', 'C:\\Python313\\Lib\\_pyrepl\\commands.py', 'PYMODULE'),
  ('_pyrepl.completing_reader',
   'C:\\Python313\\Lib\\_pyrepl\\completing_reader.py',
   'PYMODULE'),
  ('_pyrepl.console', 'C:\\Python313\\Lib\\_pyrepl\\console.py', 'PYMODULE'),
  ('_pyrepl.curses', 'C:\\Python313\\Lib\\_pyrepl\\curses.py', 'PYMODULE'),
  ('_pyrepl.fancy_termios',
   'C:\\Python313\\Lib\\_pyrepl\\fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.historical_reader',
   'C:\\Python313\\Lib\\_pyrepl\\historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.input', 'C:\\Python313\\Lib\\_pyrepl\\input.py', 'PYMODULE'),
  ('_pyrepl.keymap', 'C:\\Python313\\Lib\\_pyrepl\\keymap.py', 'PYMODULE'),
  ('_pyrepl.main', 'C:\\Python313\\Lib\\_pyrepl\\main.py', 'PYMODULE'),
  ('_pyrepl.pager', 'C:\\Python313\\Lib\\_pyrepl\\pager.py', 'PYMODULE'),
  ('_pyrepl.reader', 'C:\\Python313\\Lib\\_pyrepl\\reader.py', 'PYMODULE'),
  ('_pyrepl.readline', 'C:\\Python313\\Lib\\_pyrepl\\readline.py', 'PYMODULE'),
  ('_pyrepl.simple_interact',
   'C:\\Python313\\Lib\\_pyrepl\\simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.trace', 'C:\\Python313\\Lib\\_pyrepl\\trace.py', 'PYMODULE'),
  ('_pyrepl.types', 'C:\\Python313\\Lib\\_pyrepl\\types.py', 'PYMODULE'),
  ('_pyrepl.unix_console',
   'C:\\Python313\\Lib\\_pyrepl\\unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   'C:\\Python313\\Lib\\_pyrepl\\unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.utils', 'C:\\Python313\\Lib\\_pyrepl\\utils.py', 'PYMODULE'),
  ('_pyrepl.windows_console',
   'C:\\Python313\\Lib\\_pyrepl\\windows_console.py',
   'PYMODULE'),
  ('_pytest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\__init__.py',
   'PYMODULE'),
  ('_pytest._argcomplete',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\_argcomplete.py',
   'PYMODULE'),
  ('_pytest._code',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\_code\\__init__.py',
   'PYMODULE'),
  ('_pytest._code.code',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\_code\\code.py',
   'PYMODULE'),
  ('_pytest._code.source',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\_code\\source.py',
   'PYMODULE'),
  ('_pytest._io',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\_io\\__init__.py',
   'PYMODULE'),
  ('_pytest._io.pprint',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\_io\\pprint.py',
   'PYMODULE'),
  ('_pytest._io.saferepr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\_io\\saferepr.py',
   'PYMODULE'),
  ('_pytest._io.terminalwriter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\_io\\terminalwriter.py',
   'PYMODULE'),
  ('_pytest._io.wcwidth',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\_io\\wcwidth.py',
   'PYMODULE'),
  ('_pytest._py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\_py\\__init__.py',
   'PYMODULE'),
  ('_pytest._py.error',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\_py\\error.py',
   'PYMODULE'),
  ('_pytest._py.path',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\_py\\path.py',
   'PYMODULE'),
  ('_pytest._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\_version.py',
   'PYMODULE'),
  ('_pytest.assertion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\assertion\\__init__.py',
   'PYMODULE'),
  ('_pytest.assertion.rewrite',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\assertion\\rewrite.py',
   'PYMODULE'),
  ('_pytest.assertion.truncate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\assertion\\truncate.py',
   'PYMODULE'),
  ('_pytest.assertion.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\assertion\\util.py',
   'PYMODULE'),
  ('_pytest.cacheprovider',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\cacheprovider.py',
   'PYMODULE'),
  ('_pytest.capture',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\capture.py',
   'PYMODULE'),
  ('_pytest.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\compat.py',
   'PYMODULE'),
  ('_pytest.config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\config\\__init__.py',
   'PYMODULE'),
  ('_pytest.config.argparsing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\config\\argparsing.py',
   'PYMODULE'),
  ('_pytest.config.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\config\\compat.py',
   'PYMODULE'),
  ('_pytest.config.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\config\\exceptions.py',
   'PYMODULE'),
  ('_pytest.config.findpaths',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\config\\findpaths.py',
   'PYMODULE'),
  ('_pytest.debugging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\debugging.py',
   'PYMODULE'),
  ('_pytest.deprecated',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\deprecated.py',
   'PYMODULE'),
  ('_pytest.doctest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\doctest.py',
   'PYMODULE'),
  ('_pytest.faulthandler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\faulthandler.py',
   'PYMODULE'),
  ('_pytest.fixtures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\fixtures.py',
   'PYMODULE'),
  ('_pytest.freeze_support',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\freeze_support.py',
   'PYMODULE'),
  ('_pytest.helpconfig',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\helpconfig.py',
   'PYMODULE'),
  ('_pytest.hookspec',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\hookspec.py',
   'PYMODULE'),
  ('_pytest.junitxml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\junitxml.py',
   'PYMODULE'),
  ('_pytest.legacypath',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\legacypath.py',
   'PYMODULE'),
  ('_pytest.logging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\logging.py',
   'PYMODULE'),
  ('_pytest.main',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\main.py',
   'PYMODULE'),
  ('_pytest.mark',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\mark\\__init__.py',
   'PYMODULE'),
  ('_pytest.mark.expression',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\mark\\expression.py',
   'PYMODULE'),
  ('_pytest.mark.structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\mark\\structures.py',
   'PYMODULE'),
  ('_pytest.monkeypatch',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\monkeypatch.py',
   'PYMODULE'),
  ('_pytest.nodes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\nodes.py',
   'PYMODULE'),
  ('_pytest.outcomes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\outcomes.py',
   'PYMODULE'),
  ('_pytest.pastebin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\pastebin.py',
   'PYMODULE'),
  ('_pytest.pathlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\pathlib.py',
   'PYMODULE'),
  ('_pytest.pytester',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\pytester.py',
   'PYMODULE'),
  ('_pytest.pytester_assertions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\pytester_assertions.py',
   'PYMODULE'),
  ('_pytest.python',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\python.py',
   'PYMODULE'),
  ('_pytest.python_api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\python_api.py',
   'PYMODULE'),
  ('_pytest.python_path',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\python_path.py',
   'PYMODULE'),
  ('_pytest.recwarn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\recwarn.py',
   'PYMODULE'),
  ('_pytest.reports',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\reports.py',
   'PYMODULE'),
  ('_pytest.runner',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\runner.py',
   'PYMODULE'),
  ('_pytest.scope',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\scope.py',
   'PYMODULE'),
  ('_pytest.setuponly',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\setuponly.py',
   'PYMODULE'),
  ('_pytest.setupplan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\setupplan.py',
   'PYMODULE'),
  ('_pytest.skipping',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\skipping.py',
   'PYMODULE'),
  ('_pytest.stash',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\stash.py',
   'PYMODULE'),
  ('_pytest.stepwise',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\stepwise.py',
   'PYMODULE'),
  ('_pytest.terminal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\terminal.py',
   'PYMODULE'),
  ('_pytest.threadexception',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\threadexception.py',
   'PYMODULE'),
  ('_pytest.timing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\timing.py',
   'PYMODULE'),
  ('_pytest.tmpdir',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\tmpdir.py',
   'PYMODULE'),
  ('_pytest.unittest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\unittest.py',
   'PYMODULE'),
  ('_pytest.unraisableexception',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\unraisableexception.py',
   'PYMODULE'),
  ('_pytest.warning_types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\warning_types.py',
   'PYMODULE'),
  ('_pytest.warnings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\warnings.py',
   'PYMODULE'),
  ('_sitebuiltins', 'C:\\Python313\\Lib\\_sitebuiltins.py', 'PYMODULE'),
  ('_strptime', 'C:\\Python313\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'C:\\Python313\\Lib\\_threading_local.py', 'PYMODULE'),
  ('aiofiles',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\aiofiles\\__init__.py',
   'PYMODULE'),
  ('aiofiles.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\aiofiles\\base.py',
   'PYMODULE'),
  ('aiofiles.tempfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\aiofiles\\tempfile\\__init__.py',
   'PYMODULE'),
  ('aiofiles.tempfile.temptypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\aiofiles\\tempfile\\temptypes.py',
   'PYMODULE'),
  ('aiofiles.threadpool',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\aiofiles\\threadpool\\__init__.py',
   'PYMODULE'),
  ('aiofiles.threadpool.binary',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\aiofiles\\threadpool\\binary.py',
   'PYMODULE'),
  ('aiofiles.threadpool.text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\aiofiles\\threadpool\\text.py',
   'PYMODULE'),
  ('aiofiles.threadpool.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\aiofiles\\threadpool\\utils.py',
   'PYMODULE'),
  ('annotated_types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\annotated_types\\__init__.py',
   'PYMODULE'),
  ('anyio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\__init__.py',
   'PYMODULE'),
  ('anyio._backends',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\_backends\\__init__.py',
   'PYMODULE'),
  ('anyio._backends._asyncio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\_backends\\_asyncio.py',
   'PYMODULE'),
  ('anyio._backends._trio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\_backends\\_trio.py',
   'PYMODULE'),
  ('anyio._core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\_core\\__init__.py',
   'PYMODULE'),
  ('anyio._core._asyncio_selector_thread',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\_core\\_asyncio_selector_thread.py',
   'PYMODULE'),
  ('anyio._core._eventloop',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\_core\\_eventloop.py',
   'PYMODULE'),
  ('anyio._core._exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\_core\\_exceptions.py',
   'PYMODULE'),
  ('anyio._core._fileio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\_core\\_fileio.py',
   'PYMODULE'),
  ('anyio._core._resources',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\_core\\_resources.py',
   'PYMODULE'),
  ('anyio._core._signals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\_core\\_signals.py',
   'PYMODULE'),
  ('anyio._core._sockets',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\_core\\_sockets.py',
   'PYMODULE'),
  ('anyio._core._streams',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\_core\\_streams.py',
   'PYMODULE'),
  ('anyio._core._subprocesses',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\_core\\_subprocesses.py',
   'PYMODULE'),
  ('anyio._core._synchronization',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\_core\\_synchronization.py',
   'PYMODULE'),
  ('anyio._core._tasks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\_core\\_tasks.py',
   'PYMODULE'),
  ('anyio._core._tempfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\_core\\_tempfile.py',
   'PYMODULE'),
  ('anyio._core._testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\_core\\_testing.py',
   'PYMODULE'),
  ('anyio._core._typedattr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\_core\\_typedattr.py',
   'PYMODULE'),
  ('anyio.abc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\abc\\__init__.py',
   'PYMODULE'),
  ('anyio.abc._eventloop',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\abc\\_eventloop.py',
   'PYMODULE'),
  ('anyio.abc._resources',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\abc\\_resources.py',
   'PYMODULE'),
  ('anyio.abc._sockets',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\abc\\_sockets.py',
   'PYMODULE'),
  ('anyio.abc._streams',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\abc\\_streams.py',
   'PYMODULE'),
  ('anyio.abc._subprocesses',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\abc\\_subprocesses.py',
   'PYMODULE'),
  ('anyio.abc._tasks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\abc\\_tasks.py',
   'PYMODULE'),
  ('anyio.abc._testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\abc\\_testing.py',
   'PYMODULE'),
  ('anyio.from_thread',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\from_thread.py',
   'PYMODULE'),
  ('anyio.lowlevel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\lowlevel.py',
   'PYMODULE'),
  ('anyio.streams',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\streams\\__init__.py',
   'PYMODULE'),
  ('anyio.streams.memory',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\streams\\memory.py',
   'PYMODULE'),
  ('anyio.streams.stapled',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\streams\\stapled.py',
   'PYMODULE'),
  ('anyio.streams.tls',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\streams\\tls.py',
   'PYMODULE'),
  ('anyio.to_thread',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\anyio\\to_thread.py',
   'PYMODULE'),
  ('argparse', 'C:\\Python313\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'C:\\Python313\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'C:\\Python313\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Python313\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Python313\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Python313\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Python313\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Python313\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Python313\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events', 'C:\\Python313\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Python313\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Python313\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures', 'C:\\Python313\\Lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.locks', 'C:\\Python313\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.log', 'C:\\Python313\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.mixins', 'C:\\Python313\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Python313\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Python313\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues', 'C:\\Python313\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners', 'C:\\Python313\\Lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Python313\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto', 'C:\\Python313\\Lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Python313\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams', 'C:\\Python313\\Lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Python313\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Python313\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks', 'C:\\Python313\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.threads', 'C:\\Python313\\Lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.timeouts', 'C:\\Python313\\Lib\\asyncio\\timeouts.py', 'PYMODULE'),
  ('asyncio.transports',
   'C:\\Python313\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock', 'C:\\Python313\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Python313\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Python313\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Python313\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('attr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attr\\__init__.py',
   'PYMODULE'),
  ('attr._cmp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attr\\_cmp.py',
   'PYMODULE'),
  ('attr._compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attr\\_compat.py',
   'PYMODULE'),
  ('attr._config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attr\\_config.py',
   'PYMODULE'),
  ('attr._funcs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attr\\_funcs.py',
   'PYMODULE'),
  ('attr._make',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attr\\_make.py',
   'PYMODULE'),
  ('attr._next_gen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attr\\_next_gen.py',
   'PYMODULE'),
  ('attr._version_info',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attr\\_version_info.py',
   'PYMODULE'),
  ('attr.converters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attr\\converters.py',
   'PYMODULE'),
  ('attr.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attr\\exceptions.py',
   'PYMODULE'),
  ('attr.filters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attr\\filters.py',
   'PYMODULE'),
  ('attr.setters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attr\\setters.py',
   'PYMODULE'),
  ('attr.validators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attr\\validators.py',
   'PYMODULE'),
  ('attrs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attrs\\__init__.py',
   'PYMODULE'),
  ('attrs.converters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attrs\\converters.py',
   'PYMODULE'),
  ('attrs.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attrs\\exceptions.py',
   'PYMODULE'),
  ('attrs.filters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attrs\\filters.py',
   'PYMODULE'),
  ('attrs.setters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attrs\\setters.py',
   'PYMODULE'),
  ('attrs.validators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attrs\\validators.py',
   'PYMODULE'),
  ('backports',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'C:\\Python313\\Lib\\base64.py', 'PYMODULE'),
  ('bdb', 'C:\\Python313\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'C:\\Python313\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'C:\\Python313\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'C:\\Python313\\Lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cffi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.model',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('click',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click._compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._termui_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click._textwrap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click._winconsole',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.decorators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.formatting',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click.globals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.termui',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('click.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('cmd', 'C:\\Python313\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'C:\\Python313\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'C:\\Python313\\Lib\\codeop.py', 'PYMODULE'),
  ('colorama',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorsys', 'C:\\Python313\\Lib\\colorsys.py', 'PYMODULE'),
  ('concurrent', 'C:\\Python313\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('concurrent.futures',
   'C:\\Python313\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Python313\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Python313\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Python313\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser', 'C:\\Python313\\Lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'C:\\Python313\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'C:\\Python313\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'C:\\Python313\\Lib\\copy.py', 'PYMODULE'),
  ('cryptography',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.fernet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\fernet.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.aead',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\backends\\openssl\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ciphers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ciphers.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.cmac',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\backends\\openssl\\cmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.decode_asn1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\backends\\openssl\\decode_asn1.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ec',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.rsa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\backends\\openssl\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\backends\\openssl\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hmac',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\hmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.padding',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.pkcs12',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\pkcs12.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('csv', 'C:\\Python313\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'C:\\Python313\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix', 'C:\\Python313\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes._endian', 'C:\\Python313\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Python313\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Python313\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Python313\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Python313\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util', 'C:\\Python313\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes.wintypes', 'C:\\Python313\\Lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('curses', 'C:\\Python313\\Lib\\curses\\__init__.py', 'PYMODULE'),
  ('curses.has_key', 'C:\\Python313\\Lib\\curses\\has_key.py', 'PYMODULE'),
  ('dataclasses', 'C:\\Python313\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'C:\\Python313\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'C:\\Python313\\Lib\\decimal.py', 'PYMODULE'),
  ('defusedxml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\defusedxml\\__init__.py',
   'PYMODULE'),
  ('defusedxml.ElementTree',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\defusedxml\\ElementTree.py',
   'PYMODULE'),
  ('defusedxml.cElementTree',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\defusedxml\\cElementTree.py',
   'PYMODULE'),
  ('defusedxml.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\defusedxml\\common.py',
   'PYMODULE'),
  ('defusedxml.expatbuilder',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\defusedxml\\expatbuilder.py',
   'PYMODULE'),
  ('defusedxml.expatreader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\defusedxml\\expatreader.py',
   'PYMODULE'),
  ('defusedxml.minidom',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\defusedxml\\minidom.py',
   'PYMODULE'),
  ('defusedxml.pulldom',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\defusedxml\\pulldom.py',
   'PYMODULE'),
  ('defusedxml.sax',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\defusedxml\\sax.py',
   'PYMODULE'),
  ('defusedxml.xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\defusedxml\\xmlrpc.py',
   'PYMODULE'),
  ('difflib', 'C:\\Python313\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'C:\\Python313\\Lib\\dis.py', 'PYMODULE'),
  ('dlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dlib\\__init__.py',
   'PYMODULE'),
  ('doctest', 'C:\\Python313\\Lib\\doctest.py', 'PYMODULE'),
  ('dotenv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dotenv\\__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dotenv\\main.py',
   'PYMODULE'),
  ('dotenv.parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('dotenv.variables',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('email', 'C:\\Python313\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'C:\\Python313\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Python313\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'C:\\Python313\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase',
   'C:\\Python313\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime', 'C:\\Python313\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'C:\\Python313\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'C:\\Python313\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'C:\\Python313\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'C:\\Python313\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'C:\\Python313\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'C:\\Python313\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'C:\\Python313\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'C:\\Python313\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'C:\\Python313\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'C:\\Python313\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'C:\\Python313\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'C:\\Python313\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'C:\\Python313\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'C:\\Python313\\Lib\\email\\utils.py', 'PYMODULE'),
  ('face_recognition',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\face_recognition\\__init__.py',
   'PYMODULE'),
  ('face_recognition.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\face_recognition\\api.py',
   'PYMODULE'),
  ('face_recognition_models',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\face_recognition_models\\__init__.py',
   'PYMODULE'),
  ('fileinput', 'C:\\Python313\\Lib\\fileinput.py', 'PYMODULE'),
  ('fnmatch', 'C:\\Python313\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'C:\\Python313\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'C:\\Python313\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'C:\\Python313\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'C:\\Python313\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'C:\\Python313\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'C:\\Python313\\Lib\\glob.py', 'PYMODULE'),
  ('greenlet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\greenlet\\__init__.py',
   'PYMODULE'),
  ('gzip', 'C:\\Python313\\Lib\\gzip.py', 'PYMODULE'),
  ('h11',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\h11\\__init__.py',
   'PYMODULE'),
  ('h11._abnf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\h11\\_abnf.py',
   'PYMODULE'),
  ('h11._connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\h11\\_connection.py',
   'PYMODULE'),
  ('h11._events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\h11\\_events.py',
   'PYMODULE'),
  ('h11._headers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\h11\\_headers.py',
   'PYMODULE'),
  ('h11._readers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\h11\\_readers.py',
   'PYMODULE'),
  ('h11._receivebuffer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\h11\\_receivebuffer.py',
   'PYMODULE'),
  ('h11._state',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\h11\\_state.py',
   'PYMODULE'),
  ('h11._util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\h11\\_util.py',
   'PYMODULE'),
  ('h11._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\h11\\_version.py',
   'PYMODULE'),
  ('h11._writers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\h11\\_writers.py',
   'PYMODULE'),
  ('hashlib', 'C:\\Python313\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'C:\\Python313\\Lib\\hmac.py', 'PYMODULE'),
  ('html', 'C:\\Python313\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'C:\\Python313\\Lib\\html\\entities.py', 'PYMODULE'),
  ('http', 'C:\\Python313\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'C:\\Python313\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'C:\\Python313\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http.server', 'C:\\Python313\\Lib\\http\\server.py', 'PYMODULE'),
  ('httpcore',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpcore\\__init__.py',
   'PYMODULE'),
  ('httpcore._api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpcore\\_api.py',
   'PYMODULE'),
  ('httpcore._async',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpcore\\_async\\__init__.py',
   'PYMODULE'),
  ('httpcore._async.connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpcore\\_async\\connection.py',
   'PYMODULE'),
  ('httpcore._async.connection_pool',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpcore\\_async\\connection_pool.py',
   'PYMODULE'),
  ('httpcore._async.http11',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpcore\\_async\\http11.py',
   'PYMODULE'),
  ('httpcore._async.http2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpcore\\_async\\http2.py',
   'PYMODULE'),
  ('httpcore._async.http_proxy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpcore\\_async\\http_proxy.py',
   'PYMODULE'),
  ('httpcore._async.interfaces',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpcore\\_async\\interfaces.py',
   'PYMODULE'),
  ('httpcore._async.socks_proxy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpcore\\_async\\socks_proxy.py',
   'PYMODULE'),
  ('httpcore._backends',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpcore\\_backends\\__init__.py',
   'PYMODULE'),
  ('httpcore._backends.anyio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpcore\\_backends\\anyio.py',
   'PYMODULE'),
  ('httpcore._backends.auto',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpcore\\_backends\\auto.py',
   'PYMODULE'),
  ('httpcore._backends.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpcore\\_backends\\base.py',
   'PYMODULE'),
  ('httpcore._backends.mock',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpcore\\_backends\\mock.py',
   'PYMODULE'),
  ('httpcore._backends.sync',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpcore\\_backends\\sync.py',
   'PYMODULE'),
  ('httpcore._backends.trio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpcore\\_backends\\trio.py',
   'PYMODULE'),
  ('httpcore._exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpcore\\_exceptions.py',
   'PYMODULE'),
  ('httpcore._models',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpcore\\_models.py',
   'PYMODULE'),
  ('httpcore._ssl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpcore\\_ssl.py',
   'PYMODULE'),
  ('httpcore._sync',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpcore\\_sync\\__init__.py',
   'PYMODULE'),
  ('httpcore._sync.connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpcore\\_sync\\connection.py',
   'PYMODULE'),
  ('httpcore._sync.connection_pool',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpcore\\_sync\\connection_pool.py',
   'PYMODULE'),
  ('httpcore._sync.http11',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpcore\\_sync\\http11.py',
   'PYMODULE'),
  ('httpcore._sync.http2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpcore\\_sync\\http2.py',
   'PYMODULE'),
  ('httpcore._sync.http_proxy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpcore\\_sync\\http_proxy.py',
   'PYMODULE'),
  ('httpcore._sync.interfaces',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpcore\\_sync\\interfaces.py',
   'PYMODULE'),
  ('httpcore._sync.socks_proxy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpcore\\_sync\\socks_proxy.py',
   'PYMODULE'),
  ('httpcore._synchronization',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpcore\\_synchronization.py',
   'PYMODULE'),
  ('httpcore._trace',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpcore\\_trace.py',
   'PYMODULE'),
  ('httpcore._utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpcore\\_utils.py',
   'PYMODULE'),
  ('httpx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpx\\__init__.py',
   'PYMODULE'),
  ('httpx.__version__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpx\\__version__.py',
   'PYMODULE'),
  ('httpx._api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpx\\_api.py',
   'PYMODULE'),
  ('httpx._auth',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpx\\_auth.py',
   'PYMODULE'),
  ('httpx._client',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpx\\_client.py',
   'PYMODULE'),
  ('httpx._config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpx\\_config.py',
   'PYMODULE'),
  ('httpx._content',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpx\\_content.py',
   'PYMODULE'),
  ('httpx._decoders',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpx\\_decoders.py',
   'PYMODULE'),
  ('httpx._exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpx\\_exceptions.py',
   'PYMODULE'),
  ('httpx._main',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpx\\_main.py',
   'PYMODULE'),
  ('httpx._models',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpx\\_models.py',
   'PYMODULE'),
  ('httpx._multipart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpx\\_multipart.py',
   'PYMODULE'),
  ('httpx._status_codes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpx\\_status_codes.py',
   'PYMODULE'),
  ('httpx._transports',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpx\\_transports\\__init__.py',
   'PYMODULE'),
  ('httpx._transports.asgi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpx\\_transports\\asgi.py',
   'PYMODULE'),
  ('httpx._transports.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpx\\_transports\\base.py',
   'PYMODULE'),
  ('httpx._transports.default',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpx\\_transports\\default.py',
   'PYMODULE'),
  ('httpx._transports.mock',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpx\\_transports\\mock.py',
   'PYMODULE'),
  ('httpx._transports.wsgi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpx\\_transports\\wsgi.py',
   'PYMODULE'),
  ('httpx._types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpx\\_types.py',
   'PYMODULE'),
  ('httpx._urlparse',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpx\\_urlparse.py',
   'PYMODULE'),
  ('httpx._urls',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpx\\_urls.py',
   'PYMODULE'),
  ('httpx._utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpx\\_utils.py',
   'PYMODULE'),
  ('idna',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib', 'C:\\Python313\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'C:\\Python313\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Python313\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Python313\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'C:\\Python313\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'C:\\Python313\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Python313\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Python313\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Python313\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Python313\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Python313\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Python313\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Python313\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Python313\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Python313\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Python313\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Python313\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'C:\\Python313\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Python313\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Python313\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Python313\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util', 'C:\\Python313\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('iniconfig',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\iniconfig\\__init__.py',
   'PYMODULE'),
  ('iniconfig._parse',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\iniconfig\\_parse.py',
   'PYMODULE'),
  ('iniconfig.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\iniconfig\\exceptions.py',
   'PYMODULE'),
  ('inspect', 'C:\\Python313\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Python313\\Lib\\ipaddress.py', 'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('json', 'C:\\Python313\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'C:\\Python313\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'C:\\Python313\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'C:\\Python313\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'C:\\Python313\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('logging.handlers', 'C:\\Python313\\Lib\\logging\\handlers.py', 'PYMODULE'),
  ('lzma', 'C:\\Python313\\Lib\\lzma.py', 'PYMODULE'),
  ('magic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\magic\\__init__.py',
   'PYMODULE'),
  ('magic.magic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\magic\\magic.py',
   'PYMODULE'),
  ('mimetypes', 'C:\\Python313\\Lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'C:\\Python313\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Python313\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Python313\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Python313\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Python313\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Python313\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Python313\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Python313\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Python313\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Python313\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Python313\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Python313\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Python313\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Python313\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Python313\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Python313\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Python313\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Python313\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Python313\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Python313\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Python313\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Python313\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Python313\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'C:\\Python313\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'C:\\Python313\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'C:\\Python313\\Lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('numpy._core.tests._natype',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'C:\\Python313\\Lib\\opcode.py', 'PYMODULE'),
  ('outcome',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\outcome\\__init__.py',
   'PYMODULE'),
  ('outcome._impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\outcome\\_impl.py',
   'PYMODULE'),
  ('outcome._util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\outcome\\_util.py',
   'PYMODULE'),
  ('outcome._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\outcome\\_version.py',
   'PYMODULE'),
  ('packaging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib', 'C:\\Python313\\Lib\\pathlib\\__init__.py', 'PYMODULE'),
  ('pathlib._abc', 'C:\\Python313\\Lib\\pathlib\\_abc.py', 'PYMODULE'),
  ('pathlib._local', 'C:\\Python313\\Lib\\pathlib\\_local.py', 'PYMODULE'),
  ('pdb', 'C:\\Python313\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'C:\\Python313\\Lib\\pickle.py', 'PYMODULE'),
  ('pkg_resources',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkgutil', 'C:\\Python313\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'C:\\Python313\\Lib\\platform.py', 'PYMODULE'),
  ('platformdirs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('platformdirs.android',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\platformdirs\\android.py',
   'PYMODULE'),
  ('platformdirs.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\platformdirs\\api.py',
   'PYMODULE'),
  ('platformdirs.macos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\platformdirs\\macos.py',
   'PYMODULE'),
  ('platformdirs.unix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\platformdirs\\unix.py',
   'PYMODULE'),
  ('platformdirs.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\platformdirs\\version.py',
   'PYMODULE'),
  ('platformdirs.windows',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\platformdirs\\windows.py',
   'PYMODULE'),
  ('plistlib', 'C:\\Python313\\Lib\\plistlib.py', 'PYMODULE'),
  ('pluggy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pluggy\\__init__.py',
   'PYMODULE'),
  ('pluggy._callers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pluggy\\_callers.py',
   'PYMODULE'),
  ('pluggy._hooks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pluggy\\_hooks.py',
   'PYMODULE'),
  ('pluggy._manager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pluggy\\_manager.py',
   'PYMODULE'),
  ('pluggy._result',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pluggy\\_result.py',
   'PYMODULE'),
  ('pluggy._tracing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pluggy\\_tracing.py',
   'PYMODULE'),
  ('pluggy._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pluggy\\_version.py',
   'PYMODULE'),
  ('pluggy._warnings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pluggy\\_warnings.py',
   'PYMODULE'),
  ('pprint', 'C:\\Python313\\Lib\\pprint.py', 'PYMODULE'),
  ('py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\py.py',
   'PYMODULE'),
  ('py_compile', 'C:\\Python313\\Lib\\py_compile.py', 'PYMODULE'),
  ('pycparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydantic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\__init__.py',
   'PYMODULE'),
  ('pydantic._internal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\_internal\\__init__.py',
   'PYMODULE'),
  ('pydantic._internal._config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\_internal\\_config.py',
   'PYMODULE'),
  ('pydantic._internal._core_metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\_internal\\_core_metadata.py',
   'PYMODULE'),
  ('pydantic._internal._core_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\_internal\\_core_utils.py',
   'PYMODULE'),
  ('pydantic._internal._dataclasses',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\_internal\\_dataclasses.py',
   'PYMODULE'),
  ('pydantic._internal._decorators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\_internal\\_decorators.py',
   'PYMODULE'),
  ('pydantic._internal._decorators_v1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\_internal\\_decorators_v1.py',
   'PYMODULE'),
  ('pydantic._internal._discriminated_union',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\_internal\\_discriminated_union.py',
   'PYMODULE'),
  ('pydantic._internal._docs_extraction',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\_internal\\_docs_extraction.py',
   'PYMODULE'),
  ('pydantic._internal._fields',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\_internal\\_fields.py',
   'PYMODULE'),
  ('pydantic._internal._forward_ref',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\_internal\\_forward_ref.py',
   'PYMODULE'),
  ('pydantic._internal._generate_schema',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\_internal\\_generate_schema.py',
   'PYMODULE'),
  ('pydantic._internal._generics',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\_internal\\_generics.py',
   'PYMODULE'),
  ('pydantic._internal._git',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\_internal\\_git.py',
   'PYMODULE'),
  ('pydantic._internal._import_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\_internal\\_import_utils.py',
   'PYMODULE'),
  ('pydantic._internal._internal_dataclass',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\_internal\\_internal_dataclass.py',
   'PYMODULE'),
  ('pydantic._internal._known_annotated_metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\_internal\\_known_annotated_metadata.py',
   'PYMODULE'),
  ('pydantic._internal._mock_val_ser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\_internal\\_mock_val_ser.py',
   'PYMODULE'),
  ('pydantic._internal._model_construction',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\_internal\\_model_construction.py',
   'PYMODULE'),
  ('pydantic._internal._namespace_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\_internal\\_namespace_utils.py',
   'PYMODULE'),
  ('pydantic._internal._repr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\_internal\\_repr.py',
   'PYMODULE'),
  ('pydantic._internal._schema_generation_shared',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\_internal\\_schema_generation_shared.py',
   'PYMODULE'),
  ('pydantic._internal._serializers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\_internal\\_serializers.py',
   'PYMODULE'),
  ('pydantic._internal._signature',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\_internal\\_signature.py',
   'PYMODULE'),
  ('pydantic._internal._std_types_schema',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\_internal\\_std_types_schema.py',
   'PYMODULE'),
  ('pydantic._internal._typing_extra',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\_internal\\_typing_extra.py',
   'PYMODULE'),
  ('pydantic._internal._utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\_internal\\_utils.py',
   'PYMODULE'),
  ('pydantic._internal._validate_call',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\_internal\\_validate_call.py',
   'PYMODULE'),
  ('pydantic._internal._validators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\_internal\\_validators.py',
   'PYMODULE'),
  ('pydantic._migration',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\_migration.py',
   'PYMODULE'),
  ('pydantic.alias_generators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\alias_generators.py',
   'PYMODULE'),
  ('pydantic.aliases',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\aliases.py',
   'PYMODULE'),
  ('pydantic.annotated_handlers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\annotated_handlers.py',
   'PYMODULE'),
  ('pydantic.class_validators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\class_validators.py',
   'PYMODULE'),
  ('pydantic.color',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\color.py',
   'PYMODULE'),
  ('pydantic.config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\config.py',
   'PYMODULE'),
  ('pydantic.dataclasses',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\dataclasses.py',
   'PYMODULE'),
  ('pydantic.datetime_parse',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\datetime_parse.py',
   'PYMODULE'),
  ('pydantic.decorator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\decorator.py',
   'PYMODULE'),
  ('pydantic.deprecated',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\deprecated\\__init__.py',
   'PYMODULE'),
  ('pydantic.deprecated.class_validators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\deprecated\\class_validators.py',
   'PYMODULE'),
  ('pydantic.deprecated.config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\deprecated\\config.py',
   'PYMODULE'),
  ('pydantic.deprecated.copy_internals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\deprecated\\copy_internals.py',
   'PYMODULE'),
  ('pydantic.deprecated.decorator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\deprecated\\decorator.py',
   'PYMODULE'),
  ('pydantic.deprecated.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\deprecated\\json.py',
   'PYMODULE'),
  ('pydantic.deprecated.parse',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\deprecated\\parse.py',
   'PYMODULE'),
  ('pydantic.deprecated.tools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\deprecated\\tools.py',
   'PYMODULE'),
  ('pydantic.env_settings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\env_settings.py',
   'PYMODULE'),
  ('pydantic.error_wrappers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\error_wrappers.py',
   'PYMODULE'),
  ('pydantic.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\errors.py',
   'PYMODULE'),
  ('pydantic.experimental',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\experimental\\__init__.py',
   'PYMODULE'),
  ('pydantic.experimental.pipeline',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\experimental\\pipeline.py',
   'PYMODULE'),
  ('pydantic.fields',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\fields.py',
   'PYMODULE'),
  ('pydantic.functional_serializers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\functional_serializers.py',
   'PYMODULE'),
  ('pydantic.functional_validators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\functional_validators.py',
   'PYMODULE'),
  ('pydantic.generics',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\generics.py',
   'PYMODULE'),
  ('pydantic.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\json.py',
   'PYMODULE'),
  ('pydantic.json_schema',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\json_schema.py',
   'PYMODULE'),
  ('pydantic.main',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\main.py',
   'PYMODULE'),
  ('pydantic.mypy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\mypy.py',
   'PYMODULE'),
  ('pydantic.networks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\networks.py',
   'PYMODULE'),
  ('pydantic.parse',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\parse.py',
   'PYMODULE'),
  ('pydantic.plugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\plugin\\__init__.py',
   'PYMODULE'),
  ('pydantic.plugin._loader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\plugin\\_loader.py',
   'PYMODULE'),
  ('pydantic.plugin._schema_validator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\plugin\\_schema_validator.py',
   'PYMODULE'),
  ('pydantic.root_model',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\root_model.py',
   'PYMODULE'),
  ('pydantic.schema',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\schema.py',
   'PYMODULE'),
  ('pydantic.tools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\tools.py',
   'PYMODULE'),
  ('pydantic.type_adapter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\type_adapter.py',
   'PYMODULE'),
  ('pydantic.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\types.py',
   'PYMODULE'),
  ('pydantic.typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\typing.py',
   'PYMODULE'),
  ('pydantic.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\utils.py',
   'PYMODULE'),
  ('pydantic.v1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\v1\\__init__.py',
   'PYMODULE'),
  ('pydantic.v1._hypothesis_plugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\v1\\_hypothesis_plugin.py',
   'PYMODULE'),
  ('pydantic.v1.annotated_types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\v1\\annotated_types.py',
   'PYMODULE'),
  ('pydantic.v1.class_validators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\v1\\class_validators.py',
   'PYMODULE'),
  ('pydantic.v1.color',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\v1\\color.py',
   'PYMODULE'),
  ('pydantic.v1.config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\v1\\config.py',
   'PYMODULE'),
  ('pydantic.v1.dataclasses',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\v1\\dataclasses.py',
   'PYMODULE'),
  ('pydantic.v1.datetime_parse',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\v1\\datetime_parse.py',
   'PYMODULE'),
  ('pydantic.v1.decorator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\v1\\decorator.py',
   'PYMODULE'),
  ('pydantic.v1.env_settings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\v1\\env_settings.py',
   'PYMODULE'),
  ('pydantic.v1.error_wrappers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\v1\\error_wrappers.py',
   'PYMODULE'),
  ('pydantic.v1.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\v1\\errors.py',
   'PYMODULE'),
  ('pydantic.v1.fields',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\v1\\fields.py',
   'PYMODULE'),
  ('pydantic.v1.generics',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\v1\\generics.py',
   'PYMODULE'),
  ('pydantic.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\v1\\json.py',
   'PYMODULE'),
  ('pydantic.v1.main',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\v1\\main.py',
   'PYMODULE'),
  ('pydantic.v1.mypy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\v1\\mypy.py',
   'PYMODULE'),
  ('pydantic.v1.networks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\v1\\networks.py',
   'PYMODULE'),
  ('pydantic.v1.parse',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\v1\\parse.py',
   'PYMODULE'),
  ('pydantic.v1.schema',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\v1\\schema.py',
   'PYMODULE'),
  ('pydantic.v1.tools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\v1\\tools.py',
   'PYMODULE'),
  ('pydantic.v1.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\v1\\types.py',
   'PYMODULE'),
  ('pydantic.v1.typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\v1\\typing.py',
   'PYMODULE'),
  ('pydantic.v1.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\v1\\utils.py',
   'PYMODULE'),
  ('pydantic.v1.validators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\v1\\validators.py',
   'PYMODULE'),
  ('pydantic.v1.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\v1\\version.py',
   'PYMODULE'),
  ('pydantic.validate_call_decorator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\validate_call_decorator.py',
   'PYMODULE'),
  ('pydantic.validators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\validators.py',
   'PYMODULE'),
  ('pydantic.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\version.py',
   'PYMODULE'),
  ('pydantic.warnings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic\\warnings.py',
   'PYMODULE'),
  ('pydantic_core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic_core\\__init__.py',
   'PYMODULE'),
  ('pydantic_core.core_schema',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pydantic_core\\core_schema.py',
   'PYMODULE'),
  ('pydoc', 'C:\\Python313\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data', 'C:\\Python313\\Lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Python313\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pyreadline',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline\\__init__.py',
   'PYMODULE'),
  ('pyreadline.clipboard',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pyreadline.clipboard.ironpython_clipboard',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline\\clipboard\\ironpython_clipboard.py',
   'PYMODULE'),
  ('pyreadline.clipboard.no_clipboard',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline\\clipboard\\no_clipboard.py',
   'PYMODULE'),
  ('pyreadline.clipboard.win32_clipboard',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline\\clipboard\\win32_clipboard.py',
   'PYMODULE'),
  ('pyreadline.console',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline\\console\\__init__.py',
   'PYMODULE'),
  ('pyreadline.console.ansi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline\\console\\ansi.py',
   'PYMODULE'),
  ('pyreadline.console.console',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline\\console\\console.py',
   'PYMODULE'),
  ('pyreadline.console.event',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline\\console\\event.py',
   'PYMODULE'),
  ('pyreadline.console.ironpython_console',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline\\console\\ironpython_console.py',
   'PYMODULE'),
  ('pyreadline.error',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline\\error.py',
   'PYMODULE'),
  ('pyreadline.keysyms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline\\keysyms\\__init__.py',
   'PYMODULE'),
  ('pyreadline.keysyms.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline\\keysyms\\common.py',
   'PYMODULE'),
  ('pyreadline.keysyms.ironpython_keysyms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline\\keysyms\\ironpython_keysyms.py',
   'PYMODULE'),
  ('pyreadline.keysyms.keysyms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline\\keysyms\\keysyms.py',
   'PYMODULE'),
  ('pyreadline.keysyms.winconstants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline\\keysyms\\winconstants.py',
   'PYMODULE'),
  ('pyreadline.lineeditor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline\\lineeditor\\__init__.py',
   'PYMODULE'),
  ('pyreadline.lineeditor.history',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline\\lineeditor\\history.py',
   'PYMODULE'),
  ('pyreadline.lineeditor.lineobj',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline\\lineeditor\\lineobj.py',
   'PYMODULE'),
  ('pyreadline.lineeditor.wordmatcher',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline\\lineeditor\\wordmatcher.py',
   'PYMODULE'),
  ('pyreadline.logger',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline\\logger.py',
   'PYMODULE'),
  ('pyreadline.modes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline\\modes\\__init__.py',
   'PYMODULE'),
  ('pyreadline.modes.basemode',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline\\modes\\basemode.py',
   'PYMODULE'),
  ('pyreadline.modes.emacs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline\\modes\\emacs.py',
   'PYMODULE'),
  ('pyreadline.modes.notemacs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline\\modes\\notemacs.py',
   'PYMODULE'),
  ('pyreadline.modes.vi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline\\modes\\vi.py',
   'PYMODULE'),
  ('pyreadline.py3k_compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline\\py3k_compat.py',
   'PYMODULE'),
  ('pyreadline.release',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline\\release.py',
   'PYMODULE'),
  ('pyreadline.rlmain',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline\\rlmain.py',
   'PYMODULE'),
  ('pyreadline.unicode_helper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline\\unicode_helper.py',
   'PYMODULE'),
  ('pytest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pytest\\__init__.py',
   'PYMODULE'),
  ('queue', 'C:\\Python313\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'C:\\Python313\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'C:\\Python313\\Lib\\random.py', 'PYMODULE'),
  ('readline',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\readline.py',
   'PYMODULE'),
  ('rlcompleter', 'C:\\Python313\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('runpy', 'C:\\Python313\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'C:\\Python313\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'C:\\Python313\\Lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'C:\\Python313\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'C:\\Python313\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'C:\\Python313\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'C:\\Python313\\Lib\\site.py', 'PYMODULE'),
  ('smtplib', 'C:\\Python313\\Lib\\smtplib.py', 'PYMODULE'),
  ('sniffio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sniffio\\__init__.py',
   'PYMODULE'),
  ('sniffio._impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sniffio\\_impl.py',
   'PYMODULE'),
  ('sniffio._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sniffio\\_version.py',
   'PYMODULE'),
  ('socket', 'C:\\Python313\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'C:\\Python313\\Lib\\socketserver.py', 'PYMODULE'),
  ('sortedcontainers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sortedcontainers\\__init__.py',
   'PYMODULE'),
  ('sortedcontainers.sorteddict',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sortedcontainers\\sorteddict.py',
   'PYMODULE'),
  ('sortedcontainers.sortedlist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sortedcontainers\\sortedlist.py',
   'PYMODULE'),
  ('sortedcontainers.sortedset',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sortedcontainers\\sortedset.py',
   'PYMODULE'),
  ('ssl', 'C:\\Python313\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'C:\\Python313\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'C:\\Python313\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'C:\\Python313\\Lib\\stringprep.py', 'PYMODULE'),
  ('structlog',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\structlog\\__init__.py',
   'PYMODULE'),
  ('structlog._base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\structlog\\_base.py',
   'PYMODULE'),
  ('structlog._config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\structlog\\_config.py',
   'PYMODULE'),
  ('structlog._frames',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\structlog\\_frames.py',
   'PYMODULE'),
  ('structlog._generic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\structlog\\_generic.py',
   'PYMODULE'),
  ('structlog._greenlets',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\structlog\\_greenlets.py',
   'PYMODULE'),
  ('structlog._log_levels',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\structlog\\_log_levels.py',
   'PYMODULE'),
  ('structlog._native',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\structlog\\_native.py',
   'PYMODULE'),
  ('structlog._output',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\structlog\\_output.py',
   'PYMODULE'),
  ('structlog._utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\structlog\\_utils.py',
   'PYMODULE'),
  ('structlog.contextvars',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\structlog\\contextvars.py',
   'PYMODULE'),
  ('structlog.dev',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\structlog\\dev.py',
   'PYMODULE'),
  ('structlog.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\structlog\\exceptions.py',
   'PYMODULE'),
  ('structlog.processors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\structlog\\processors.py',
   'PYMODULE'),
  ('structlog.stdlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\structlog\\stdlib.py',
   'PYMODULE'),
  ('structlog.testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\structlog\\testing.py',
   'PYMODULE'),
  ('structlog.threadlocal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\structlog\\threadlocal.py',
   'PYMODULE'),
  ('structlog.tracebacks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\structlog\\tracebacks.py',
   'PYMODULE'),
  ('structlog.twisted',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\structlog\\twisted.py',
   'PYMODULE'),
  ('structlog.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\structlog\\types.py',
   'PYMODULE'),
  ('structlog.typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\structlog\\typing.py',
   'PYMODULE'),
  ('subprocess', 'C:\\Python313\\Lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'C:\\Python313\\Lib\\sysconfig\\__init__.py', 'PYMODULE'),
  ('tarfile', 'C:\\Python313\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'C:\\Python313\\Lib\\tempfile.py', 'PYMODULE'),
  ('tenacity',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tenacity\\__init__.py',
   'PYMODULE'),
  ('tenacity._utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tenacity\\_utils.py',
   'PYMODULE'),
  ('tenacity.after',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tenacity\\after.py',
   'PYMODULE'),
  ('tenacity.asyncio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tenacity\\asyncio\\__init__.py',
   'PYMODULE'),
  ('tenacity.asyncio.retry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tenacity\\asyncio\\retry.py',
   'PYMODULE'),
  ('tenacity.before',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tenacity\\before.py',
   'PYMODULE'),
  ('tenacity.before_sleep',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tenacity\\before_sleep.py',
   'PYMODULE'),
  ('tenacity.nap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tenacity\\nap.py',
   'PYMODULE'),
  ('tenacity.retry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tenacity\\retry.py',
   'PYMODULE'),
  ('tenacity.stop',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tenacity\\stop.py',
   'PYMODULE'),
  ('tenacity.tornadoweb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tenacity\\tornadoweb.py',
   'PYMODULE'),
  ('tenacity.wait',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tenacity\\wait.py',
   'PYMODULE'),
  ('textwrap', 'C:\\Python313\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'C:\\Python313\\Lib\\threading.py', 'PYMODULE'),
  ('threadpoolctl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\threadpoolctl.py',
   'PYMODULE'),
  ('token', 'C:\\Python313\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'C:\\Python313\\Lib\\tokenize.py', 'PYMODULE'),
  ('tomllib', 'C:\\Python313\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser', 'C:\\Python313\\Lib\\tomllib\\_parser.py', 'PYMODULE'),
  ('tomllib._re', 'C:\\Python313\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('tomllib._types', 'C:\\Python313\\Lib\\tomllib\\_types.py', 'PYMODULE'),
  ('tracemalloc', 'C:\\Python313\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('trio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\__init__.py',
   'PYMODULE'),
  ('trio._abc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_abc.py',
   'PYMODULE'),
  ('trio._channel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_channel.py',
   'PYMODULE'),
  ('trio._core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_core\\__init__.py',
   'PYMODULE'),
  ('trio._core._asyncgens',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_core\\_asyncgens.py',
   'PYMODULE'),
  ('trio._core._concat_tb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_core\\_concat_tb.py',
   'PYMODULE'),
  ('trio._core._entry_queue',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_core\\_entry_queue.py',
   'PYMODULE'),
  ('trio._core._exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_core\\_exceptions.py',
   'PYMODULE'),
  ('trio._core._generated_instrumentation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_core\\_generated_instrumentation.py',
   'PYMODULE'),
  ('trio._core._generated_io_epoll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_core\\_generated_io_epoll.py',
   'PYMODULE'),
  ('trio._core._generated_io_kqueue',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_core\\_generated_io_kqueue.py',
   'PYMODULE'),
  ('trio._core._generated_io_windows',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_core\\_generated_io_windows.py',
   'PYMODULE'),
  ('trio._core._generated_run',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_core\\_generated_run.py',
   'PYMODULE'),
  ('trio._core._instrumentation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_core\\_instrumentation.py',
   'PYMODULE'),
  ('trio._core._io_common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_core\\_io_common.py',
   'PYMODULE'),
  ('trio._core._io_epoll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_core\\_io_epoll.py',
   'PYMODULE'),
  ('trio._core._io_kqueue',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_core\\_io_kqueue.py',
   'PYMODULE'),
  ('trio._core._io_windows',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_core\\_io_windows.py',
   'PYMODULE'),
  ('trio._core._ki',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_core\\_ki.py',
   'PYMODULE'),
  ('trio._core._local',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_core\\_local.py',
   'PYMODULE'),
  ('trio._core._mock_clock',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_core\\_mock_clock.py',
   'PYMODULE'),
  ('trio._core._parking_lot',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_core\\_parking_lot.py',
   'PYMODULE'),
  ('trio._core._run',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_core\\_run.py',
   'PYMODULE'),
  ('trio._core._run_context',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_core\\_run_context.py',
   'PYMODULE'),
  ('trio._core._thread_cache',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_core\\_thread_cache.py',
   'PYMODULE'),
  ('trio._core._traps',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_core\\_traps.py',
   'PYMODULE'),
  ('trio._core._unbounded_queue',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_core\\_unbounded_queue.py',
   'PYMODULE'),
  ('trio._core._wakeup_socketpair',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_core\\_wakeup_socketpair.py',
   'PYMODULE'),
  ('trio._core._windows_cffi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_core\\_windows_cffi.py',
   'PYMODULE'),
  ('trio._deprecate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_deprecate.py',
   'PYMODULE'),
  ('trio._dtls',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_dtls.py',
   'PYMODULE'),
  ('trio._file_io',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_file_io.py',
   'PYMODULE'),
  ('trio._highlevel_generic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_highlevel_generic.py',
   'PYMODULE'),
  ('trio._highlevel_open_tcp_listeners',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_highlevel_open_tcp_listeners.py',
   'PYMODULE'),
  ('trio._highlevel_open_tcp_stream',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_highlevel_open_tcp_stream.py',
   'PYMODULE'),
  ('trio._highlevel_open_unix_stream',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_highlevel_open_unix_stream.py',
   'PYMODULE'),
  ('trio._highlevel_serve_listeners',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_highlevel_serve_listeners.py',
   'PYMODULE'),
  ('trio._highlevel_socket',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_highlevel_socket.py',
   'PYMODULE'),
  ('trio._highlevel_ssl_helpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_highlevel_ssl_helpers.py',
   'PYMODULE'),
  ('trio._path',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_path.py',
   'PYMODULE'),
  ('trio._signals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_signals.py',
   'PYMODULE'),
  ('trio._socket',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_socket.py',
   'PYMODULE'),
  ('trio._ssl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_ssl.py',
   'PYMODULE'),
  ('trio._subprocess',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_subprocess.py',
   'PYMODULE'),
  ('trio._subprocess_platform',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_subprocess_platform\\__init__.py',
   'PYMODULE'),
  ('trio._subprocess_platform.kqueue',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_subprocess_platform\\kqueue.py',
   'PYMODULE'),
  ('trio._subprocess_platform.waitid',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_subprocess_platform\\waitid.py',
   'PYMODULE'),
  ('trio._subprocess_platform.windows',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_subprocess_platform\\windows.py',
   'PYMODULE'),
  ('trio._sync',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_sync.py',
   'PYMODULE'),
  ('trio._threads',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_threads.py',
   'PYMODULE'),
  ('trio._timeouts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_timeouts.py',
   'PYMODULE'),
  ('trio._unix_pipes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_unix_pipes.py',
   'PYMODULE'),
  ('trio._util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_util.py',
   'PYMODULE'),
  ('trio._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_version.py',
   'PYMODULE'),
  ('trio._wait_for_object',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_wait_for_object.py',
   'PYMODULE'),
  ('trio._windows_pipes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\_windows_pipes.py',
   'PYMODULE'),
  ('trio.abc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\abc.py',
   'PYMODULE'),
  ('trio.from_thread',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\from_thread.py',
   'PYMODULE'),
  ('trio.lowlevel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\lowlevel.py',
   'PYMODULE'),
  ('trio.socket',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\socket.py',
   'PYMODULE'),
  ('trio.testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\testing\\__init__.py',
   'PYMODULE'),
  ('trio.testing._check_streams',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\testing\\_check_streams.py',
   'PYMODULE'),
  ('trio.testing._checkpoints',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\testing\\_checkpoints.py',
   'PYMODULE'),
  ('trio.testing._memory_streams',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\testing\\_memory_streams.py',
   'PYMODULE'),
  ('trio.testing._network',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\testing\\_network.py',
   'PYMODULE'),
  ('trio.testing._raises_group',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\testing\\_raises_group.py',
   'PYMODULE'),
  ('trio.testing._sequencer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\testing\\_sequencer.py',
   'PYMODULE'),
  ('trio.testing._trio_test',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\testing\\_trio_test.py',
   'PYMODULE'),
  ('trio.to_thread',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\trio\\to_thread.py',
   'PYMODULE'),
  ('tty', 'C:\\Python313\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'C:\\Python313\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('tzdata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Africa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\Africa\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\America\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Argentina',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Indiana',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Kentucky',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\America\\Kentucky\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.North_Dakota',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Antarctica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\Antarctica\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Arctic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\Arctic\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Asia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\Asia\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Atlantic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\Atlantic\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Australia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\Australia\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Brazil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\Brazil\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Canada',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\Canada\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Chile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\Chile\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Etc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\Etc\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Europe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\Europe\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Indian',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\Indian\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Mexico',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\Mexico\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Pacific',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\Pacific\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.US',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\US\\__init__.py',
   'PYMODULE'),
  ('unittest', 'C:\\Python313\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest._log', 'C:\\Python313\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('unittest.async_case',
   'C:\\Python313\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case', 'C:\\Python313\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest.loader', 'C:\\Python313\\Lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.main', 'C:\\Python313\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.mock', 'C:\\Python313\\Lib\\unittest\\mock.py', 'PYMODULE'),
  ('unittest.result', 'C:\\Python313\\Lib\\unittest\\result.py', 'PYMODULE'),
  ('unittest.runner', 'C:\\Python313\\Lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.signals', 'C:\\Python313\\Lib\\unittest\\signals.py', 'PYMODULE'),
  ('unittest.suite', 'C:\\Python313\\Lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.util', 'C:\\Python313\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('urllib', 'C:\\Python313\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'C:\\Python313\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'C:\\Python313\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request', 'C:\\Python313\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('urllib.response', 'C:\\Python313\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('uuid', 'C:\\Python313\\Lib\\uuid.py', 'PYMODULE'),
  ('webbrowser', 'C:\\Python313\\Lib\\webbrowser.py', 'PYMODULE'),
  ('win32con',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32evtlogutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('winerror',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('xml', 'C:\\Python313\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.dom', 'C:\\Python313\\Lib\\xml\\dom\\__init__.py', 'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\Python313\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg', 'C:\\Python313\\Lib\\xml\\dom\\domreg.py', 'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\Python313\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\Python313\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom', 'C:\\Python313\\Lib\\xml\\dom\\minidom.py', 'PYMODULE'),
  ('xml.dom.pulldom', 'C:\\Python313\\Lib\\xml\\dom\\pulldom.py', 'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\Python313\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree', 'C:\\Python313\\Lib\\xml\\etree\\__init__.py', 'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Python313\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Python313\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Python313\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Python313\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers', 'C:\\Python313\\Lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Python313\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax', 'C:\\Python313\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Python313\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Python313\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler', 'C:\\Python313\\Lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax.saxutils', 'C:\\Python313\\Lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Python313\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc', 'C:\\Python313\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client', 'C:\\Python313\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('xmlrpc.server', 'C:\\Python313\\Lib\\xmlrpc\\server.py', 'PYMODULE'),
  ('yaml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\__init__.py',
   'PYMODULE'),
  ('yaml.composer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.dumper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.error',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('yaml.events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.loader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.reader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.representer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.resolver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.scanner',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.serializer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.tokens',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('zipfile', 'C:\\Python313\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'C:\\Python313\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Python313\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport', 'C:\\Python313\\Lib\\zipimport.py', 'PYMODULE'),
  ('zoneinfo', 'C:\\Python313\\Lib\\zoneinfo\\__init__.py', 'PYMODULE'),
  ('zoneinfo._common', 'C:\\Python313\\Lib\\zoneinfo\\_common.py', 'PYMODULE'),
  ('zoneinfo._tzpath', 'C:\\Python313\\Lib\\zoneinfo\\_tzpath.py', 'PYMODULE'),
  ('zoneinfo._zoneinfo',
   'C:\\Python313\\Lib\\zoneinfo\\_zoneinfo.py',
   'PYMODULE'),
  ('zstandard',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zstandard\\__init__.py',
   'PYMODULE'),
  ('zstandard.backend_cffi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zstandard\\backend_cffi.py',
   'PYMODULE')])
