#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Instagram API Configuration Fix Script
This script updates the config.json to use the working RocketAPI instead of the non-functional instagram-scraper-stable-api.
"""

import json
import os
import shutil
from datetime import datetime

def backup_config():
    """Create a backup of the current config.json"""
    if os.path.exists('config.json'):
        backup_name = f"config_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        shutil.copy2('config.json', backup_name)
        print(f"✓ Created backup: {backup_name}")
        return backup_name
    return None

def update_config():
    """Update config.json to use the working RocketAPI"""
    try:
        # Read current config
        with open('config.json', 'r') as f:
            config = json.load(f)
        
        # Update Instagram API configuration
        if 'platforms' in config and 'instagram' in config['platforms']:
            config['platforms']['instagram'].update({
                "api_endpoint": "https://rocketapi-for-developers.p.rapidapi.com/instagram/search",
                "rapidapi_host": "rocketapi-for-developers.p.rapidapi.com",
                "rapidapi_key": "**************************************************"
            })
            
            # Write updated config
            with open('config.json', 'w') as f:
                json.dump(config, f, indent=2)
            
            print("✓ Updated config.json with working RocketAPI configuration")
            return True
        else:
            print("✗ Instagram configuration not found in config.json")
            return False
            
    except Exception as e:
        print(f"✗ Error updating config: {e}")
        return False

def main():
    print("=" * 60)
    print("Instagram API Configuration Fix".center(60))
    print("=" * 60)
    print()
    
    # Create backup
    backup_file = backup_config()
    
    # Update configuration
    if update_config():
        print("\n🎉 Configuration updated successfully!")
        print("\nNext steps:")
        print("1. Run 'python test_instagram_api.py' to verify the fix")
        print("2. Test your main application")
        print(f"3. If issues occur, restore from backup: {backup_file}")
    else:
        print("\n❌ Failed to update configuration")
        if backup_file:
            print(f"Your original config is backed up as: {backup_file}")

if __name__ == "__main__":
    main()
