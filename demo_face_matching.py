#!/usr/bin/env python3
"""
Demo script to show how to add face encodings to the index for matching
"""

import asyncio
import json
import cv2
import numpy as np
from pathlib import Path
from kimi2 import SocialMapperPro

async def demo_face_matching():
    """Demonstrate face matching capabilities"""
    
    # Load configuration
    cfg = json.loads(Path("config.json").read_text())
    app = SocialMapperPro(cfg)
    
    try:
        await app.initialize()
        
        # Add some sample face encodings to the index
        print("Adding sample face encodings to the index...")
        
        # Load the profile image
        img_path = Path("profile.jpg")
        if img_path.exists():
            img = cv2.imread(str(img_path))
            if img is not None:
                # Extract face encoding from the profile image
                gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
                face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
                faces = face_cascade.detectMultiScale(gray, 1.1, 4)
                
                if len(faces) > 0:
                    x, y, w, h = faces[0]  # Take the first face
                    face_img = img[y:y+h, x:x+w]
                    
                    # Create basic encoding (histogram)
                    face_resized = cv2.resize(face_img, (64, 64))
                    hist = cv2.calcHist([face_resized], [0, 1, 2], None, [8, 8, 8], [0, 256, 0, 256, 0, 256])
                    encoding = hist.flatten().astype(np.float32)
                    encoding = encoding / np.linalg.norm(encoding)  # normalize
                    
                    # Add to index
                    if app.analyzer.index:
                        app.analyzer.index.add(encoding, "virat.kohli")
                        print("✓ Added face encoding for 'virat.kohli'")
                    
                    # Add a few more sample encodings (slightly modified versions)
                    for i in range(3):
                        # Add some noise to create variations
                        noisy_encoding = encoding + np.random.normal(0, 0.01, encoding.shape)
                        noisy_encoding = noisy_encoding / np.linalg.norm(noisy_encoding)
                        app.analyzer.index.add(noisy_encoding, f"sample_user_{i}")
                        print(f"✓ Added face encoding for 'sample_user_{i}'")
        
        # Now run the full analysis
        print("\nRunning full analysis with face matching...")
        res = await app.full_analysis(
            name=cfg["search"]["default_name"],
            photo=Path(cfg["search"]["photo_path"]) if cfg["search"]["photo_path"] else None,
            platforms=cfg["search"]["platforms_to_search"],
        )
        
        print("\n" + "="*60)
        print("ANALYSIS RESULTS")
        print("="*60)
        
        # Display Instagram results
        if "instagram" in res["platforms"]:
            instagram_matches = res["platforms"]["instagram"]["matches"]
            print(f"\n📱 Instagram Search Results ({len(instagram_matches)} matches):")
            for match in instagram_matches:
                verified = "✓" if match["verified"] else "✗"
                print(f"  {verified} @{match['username']} - {match['name']}")
        
        # Display image analysis results
        if "image" in res:
            img_res = res["image"]
            print(f"\n🔍 Image Analysis Results:")
            print(f"  Status: {img_res['status']}")
            print(f"  CV Type: {img_res.get('cv_type', 'N/A')}")
            print(f"  Faces Detected: {img_res.get('faces_detected', 0)}")
            print(f"  Encodings Generated: {img_res.get('encodings', 0)}")
            
            if img_res.get('matches'):
                print(f"  Face Matches Found: {len(img_res['matches'])}")
                for match in img_res['matches']:
                    confidence = match['confidence'] * 100
                    print(f"    - {match['username']}: {confidence:.1f}% confidence")
            else:
                print("  No face matches found (try adding more faces to the index)")
        
        print("\n" + "="*60)
        print("FULL JSON OUTPUT")
        print("="*60)
        print(json.dumps(res, indent=2))
        
    finally:
        await app.shutdown()

if __name__ == "__main__":
    asyncio.run(demo_face_matching())
