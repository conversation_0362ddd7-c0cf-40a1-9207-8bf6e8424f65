# -*- coding: utf-8 -*-
"""
Optimized Image Analysis Engine with Enhanced Facial Recognition
Key improvements:
- Better memory management and caching
- Optimized face detection pipeline
- Improved error handling and validation
- Enhanced matching algorithms with confidence scoring
- Better concurrency and performance
"""

import asyncio
import io
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, NamedTuple
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import functools
import hashlib

import numpy as np
import cv2
from PIL import Image
import aiofiles

# Assuming face_recognition and other imports are available
try:
    import face_recognition
    FACE_RECOGNITION_AVAILABLE = True
except ImportError:
    FACE_RECOGNITION_AVAILABLE = False

logger = logging.getLogger(__name__)

class FaceData(NamedTuple):
    """Structured face data container"""
    location: Tuple[int, int, int, int]  # top, right, bottom, left
    encoding: np.ndarray
    confidence: float
    landmarks: Optional[Dict]

@dataclass
class ImageAnalysisResult:
    """Structured result container"""
    status: str
    file_path: str
    file_type: Optional[str] = None
    size_bytes: Optional[int] = None
    face_count: int = 0
    profile_pic_matches: List[Dict] = None
    error: Optional[str] = None
    processing_time: Optional[float] = None
    
    def __post_init__(self):
        if self.profile_pic_matches is None:
            self.profile_pic_matches = []

class EnhancedImageProcessor:
    """Optimized image preprocessing with caching"""
    
    def __init__(self, cache_size: int = 100):
        self._cache = {}
        self._cache_size = cache_size
        
    def _get_cache_key(self, img_array: np.ndarray) -> str:
        """Generate cache key for image preprocessing"""
        return hashlib.md5(img_array.tobytes()).hexdigest()
    
    def preprocess_image(self, img_array: np.ndarray) -> np.ndarray:
        """Apply optimized preprocessing with caching"""
        cache_key = self._get_cache_key(img_array)
        
        if cache_key in self._cache:
            return self._cache[cache_key]
        
        try:
            # CLAHE (Contrast Limited Adaptive Histogram Equalization) for better results
            lab = cv2.cvtColor(img_array, cv2.COLOR_RGB2LAB)
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            lab[:, :, 0] = clahe.apply(lab[:, :, 0])
            result = cv2.cvtColor(lab, cv2.COLOR_LAB2RGB)
            
            # Cache management
            if len(self._cache) >= self._cache_size:
                # Remove oldest entry (simple FIFO)
                oldest_key = next(iter(self._cache))
                del self._cache[oldest_key]
            
            self._cache[cache_key] = result
            return result
            
        except Exception as e:
            logger.warning(f"Image preprocessing failed: {e}")
            return img_array

class OptimizedFaceDetector:
    """Optimized face detection with multiple strategies"""
    
    def __init__(self, analysis_config):
        self.analysis_config = analysis_config
        self.processor = EnhancedImageProcessor()
        
    def _validate_face_landmarks(self, img_array: np.ndarray, location: Tuple) -> bool:
        """Fast landmark validation"""
        try:
            landmarks = face_recognition.face_landmarks(img_array, face_locations=[location])
            if not landmarks:
                return False
                
            landmark_dict = landmarks[0]
            required_features = ['left_eye', 'right_eye', 'nose_tip']
            
            # Quick validation: check if required features exist and have reasonable points
            for feature in required_features:
                if feature not in landmark_dict or len(landmark_dict[feature]) == 0:
                    return False
            
            # Additional validation: eye distance check
            left_eye = np.array(landmark_dict['left_eye'])
            right_eye = np.array(landmark_dict['right_eye'])
            eye_distance = np.linalg.norm(left_eye.mean(axis=0) - right_eye.mean(axis=0))
            
            # Reasonable eye distance threshold
            face_width = location[1] - location[3]  # right - left
            if eye_distance < face_width * 0.2 or eye_distance > face_width * 0.8:
                return False
                
            return True
            
        except Exception as e:
            logger.debug(f"Landmark validation failed: {e}")
            return False
    
    def detect_faces(self, img_array: np.ndarray) -> List[FaceData]:
        """Optimized face detection with quality filtering"""
        if not FACE_RECOGNITION_AVAILABLE:
            raise ImageAnalysisError("Face recognition not available")
        
        # Preprocess image
        processed_img = self.processor.preprocess_image(img_array)
        
        # Multi-scale detection for better accuracy
        face_data_list = []
        
        # Primary detection
        face_locations = face_recognition.face_locations(
            processed_img,
            model=self.analysis_config.face_detection_model,
            number_of_times_to_upsample=1
        )
        
        for location in face_locations:
            # Quality checks
            if not self._is_face_quality_sufficient(processed_img, location):
                continue
                
            # Landmark validation
            if not self._validate_face_landmarks(processed_img, location):
                continue
            
            # Generate encoding
            encodings = face_recognition.face_encodings(
                processed_img, 
                known_face_locations=[location],
                num_jitters=2  # Reduced for performance
            )
            
            if encodings:
                # Calculate face quality score
                confidence = self._calculate_face_confidence(processed_img, location)
                
                face_data_list.append(FaceData(
                    location=location,
                    encoding=encodings[0],
                    confidence=confidence,
                    landmarks=None  # Can be populated if needed
                ))
        
        return face_data_list
    
    def _is_face_quality_sufficient(self, img_array: np.ndarray, location: Tuple) -> bool:
        """Check if detected face meets quality requirements"""
        top, right, bottom, left = location
        
        # Size check
        face_width = right - left
        face_height = bottom - top
        
        if (face_width < self.analysis_config.min_face_size or 
            face_height < self.analysis_config.min_face_size):
            return False
        
        # Aspect ratio check (faces should be roughly rectangular)
        aspect_ratio = face_width / face_height
        if aspect_ratio < 0.5 or aspect_ratio > 2.0:
            return False
        
        # Extract face region for quality analysis
        face_region = img_array[top:bottom, left:right]
        
        # Blur check on face region
        gray_face = cv2.cvtColor(face_region, cv2.COLOR_RGB2GRAY)
        blur_score = cv2.Laplacian(gray_face, cv2.CV_64F).var()
        
        return blur_score >= self.analysis_config.blur_threshold
    
    def _calculate_face_confidence(self, img_array: np.ndarray, location: Tuple) -> float:
        """Calculate confidence score for detected face"""
        try:
            top, right, bottom, left = location
            face_region = img_array[top:bottom, left:right]
            
            # Multiple quality metrics
            scores = []
            
            # 1. Blur score
            gray_face = cv2.cvtColor(face_region, cv2.COLOR_RGB2GRAY)
            blur_score = cv2.Laplacian(gray_face, cv2.CV_64F).var()
            scores.append(min(1.0, blur_score / 500.0))  # Normalize
            
            # 2. Brightness consistency
            brightness_std = np.std(gray_face)
            brightness_score = min(1.0, brightness_std / 50.0)
            scores.append(brightness_score)
            
            # 3. Size score
            face_area = (right - left) * (bottom - top)
            img_area = img_array.shape[0] * img_array.shape[1]
            size_ratio = face_area / img_area
            size_score = min(1.0, size_ratio * 10)  # Favor larger faces
            scores.append(size_score)
            
            return np.mean(scores)
            
        except Exception:
            return 0.5  # Default moderate confidence

class ImageAnalysisEngine:
    """Optimized image analysis engine with enhanced performance"""
    
    def __init__(self, security: SecurityConfig, analysis_config: ImageAnalysisConfig):
        self.security = security
        self.analysis_config = analysis_config
        self.network = NetworkManager(security)
        self.fernet: Optional[Fernet] = None
        self._init_fernet()
        
        # Initialize optimized components
        self.face_detector = OptimizedFaceDetector(analysis_config)
        self.thread_pool = ThreadPoolExecutor(max_workers=4)
        
        # Profile pic cache
        self._profile_cache = {}
        self._profile_cache_ttl = 3600  # 1 hour
        
        try:
            self.mime = magic.Magic(mime=True)
        except Exception as e:
            logger.error("Failed to initialize python-magic", exc_info=True)
            raise ImageAnalysisError("Image type detection unavailable") from e
        
        self._check_dependencies()
    
    def _init_fernet(self):
        """Initialize encryption (implementation depends on your security setup)"""
        # Your existing implementation
        pass
    
    def _check_dependencies(self):
        """Check required dependencies"""
        if not FACE_RECOGNITION_AVAILABLE:
            logger.warning("Face recognition library not available")
    
    async def analyze(self, photo_path: Path, profile_pics: List[Dict] = None) -> ImageAnalysisResult:
        """
        Optimized image analysis with better error handling and performance
        """
        start_time = asyncio.get_event_loop().time()
        log = logger.bind(photo_path=str(photo_path))
        IMAGE_ANALYSIS_COUNT.labels(status='started').inc()
        
        result = ImageAnalysisResult(
            status="pending",
            file_path=str(photo_path)
        )
        
        try:
            # Parallel file validation and loading
            validation_task = self._validate_and_load_image(photo_path)
            img_array, file_info = await validation_task
            
            result.file_type = file_info['file_type']
            result.size_bytes = file_info['size_bytes']
            
            # Face detection and analysis
            if self.analysis_config.enable_facial_recognition:
                face_data_list = await asyncio.get_event_loop().run_in_executor(
                    self.thread_pool,
                    self.face_detector.detect_faces,
                    img_array
                )
                
                result.face_count = len(face_data_list)
                
                if face_data_list and profile_pics:
                    matches = await self._compare_with_profile_pics_optimized(
                        face_data_list, profile_pics
                    )
                    result.profile_pic_matches = matches
            
            result.status = "analyzed"
            result.processing_time = asyncio.get_event_loop().time() - start_time
            IMAGE_ANALYSIS_COUNT.labels(status='success').inc()
            
            return result
            
        except Exception as e:
            log.error("Image analysis failed", exc_info=True)
            result.status = "error"
            result.error = str(e)
            result.processing_time = asyncio.get_event_loop().time() - start_time
            IMAGE_ANALYSIS_COUNT.labels(status='error').inc()
            return result
    
    async def _validate_and_load_image(self, photo_path: Path) -> Tuple[np.ndarray, Dict]:
        """Optimized image validation and loading"""
        # Validate file existence and size
        if not await asyncio.to_thread(photo_path.is_file):
            raise FileNotFoundError(f"Image not found: {photo_path}")
        
        stat = await asyncio.to_thread(photo_path.stat)
        if stat.st_size > self.security.max_file_size:
            raise ValueError(f"File size {stat.st_size} exceeds limit {self.security.max_file_size}")
        
        # Check MIME type
        file_type = await asyncio.to_thread(self.mime.from_file, str(photo_path))
        if not file_type.startswith("image/"):
            raise ValueError(f"Invalid file type: {file_type}")
        
        # Load and validate image
        async with aiofiles.open(photo_path, "rb") as f:
            image_bytes = await f.read()
        
        # Use thread pool for CPU-intensive operations
        img, img_array = await asyncio.get_event_loop().run_in_executor(
            self.thread_pool,
            self._load_and_validate_image,
            image_bytes
        )
        
        return img_array, {
            'file_type': file_type,
            'size_bytes': stat.st_size
        }
    
    def _load_and_validate_image(self, image_bytes: bytes) -> Tuple[Image.Image, np.ndarray]:
        """Load and validate image in thread pool"""
        img = Image.open(io.BytesIO(image_bytes))
        
        # Convert to RGB if necessary
        if img.mode != 'RGB':
            img = img.convert('RGB')
        
        # Size validation
        if (img.width < self.analysis_config.min_face_size or
            img.height < self.analysis_config.min_face_size):
            raise ImageAnalysisError(
                f"Image too small (min {self.analysis_config.min_face_size}px)"
            )
        
        # Convert to numpy array
        img_array = np.array(img)
        
        return img, img_array
    
    async def _compare_with_profile_pics_optimized(
        self,
        face_data_list: List[FaceData],
        profile_pics: List[Dict]
    ) -> List[Dict]:
        """Optimized profile picture comparison with caching and batching"""
        
        # Pre-load and cache profile pictures
        profile_tasks = []
        for profile in profile_pics:
            profile_tasks.append(self._load_profile_pic_cached(profile))
        
        # Process profiles with limited concurrency
        semaphore = asyncio.Semaphore(5)  # Limit concurrent downloads
        profile_results = await asyncio.gather(
            *[self._process_profile_with_semaphore(semaphore, task) 
              for task in profile_tasks],
            return_exceptions=True
        )
        
        # Filter successful profile loads
        valid_profiles = []
        for i, result in enumerate(profile_results):
            if isinstance(result, Exception):
                logger.warning(f"Failed to load profile {profile_pics[i].get('username', 'unknown')}: {result}")
                continue
            if result:
                valid_profiles.append((profile_pics[i], result))
        
        # Batch comparison
        matches = []
        for profile_info, profile_encodings in valid_profiles:
            for face_idx, face_data in enumerate(face_data_list):
                match_result = self._calculate_face_match(
                    face_data, profile_encodings, profile_info, face_idx
                )
                if match_result:
                    matches.append(match_result)
        
        # Sort by confidence and remove duplicates
        matches.sort(key=lambda x: x['confidence'], reverse=True)
        return self._deduplicate_matches(matches)
    
    async def _process_profile_with_semaphore(self, semaphore: asyncio.Semaphore, task):
        """Process profile with concurrency control"""
        async with semaphore:
            return await task
    
    async def _load_profile_pic_cached(self, profile: Dict) -> Optional[List[np.ndarray]]:
        """Load profile picture with caching"""
        pic_url = profile.get("profile_pic_url")
        if not pic_url:
            return None
        
        # Check cache
        cache_key = hashlib.md5(pic_url.encode()).hexdigest()
        if cache_key in self._profile_cache:
            cache_entry = self._profile_cache[cache_key]
            if asyncio.get_event_loop().time() - cache_entry['timestamp'] < self._profile_cache_ttl:
                return cache_entry['encodings']
        
        try:
            # Download and process
            response = await self.network.secure_request("GET", pic_url, platform="image_download")
            profile_encodings = await asyncio.get_event_loop().run_in_executor(
                self.thread_pool,
                self._process_profile_image,
                response.content
            )
            
            # Cache result
            self._profile_cache[cache_key] = {
                'encodings': profile_encodings,
                'timestamp': asyncio.get_event_loop().time()
            }
            
            return profile_encodings
            
        except Exception as e:
            logger.warning(f"Profile pic processing failed for {profile.get('username', 'unknown')}: {e}")
            return None
    
    def _process_profile_image(self, image_bytes: bytes) -> List[np.ndarray]:
        """Process profile image and extract encodings"""
        try:
            profile_img_array = face_recognition.load_image_file(io.BytesIO(image_bytes))
            face_locations = face_recognition.face_locations(
                profile_img_array,
                model=self.analysis_config.face_detection_model
            )
            
            if not face_locations:
                return []
            
            return face_recognition.face_encodings(profile_img_array, face_locations)
            
        except Exception as e:
            logger.debug(f"Profile image processing failed: {e}")
            return []
    
    def _calculate_face_match(
        self,
        face_data: FaceData,
        profile_encodings: List[np.ndarray],
        profile_info: Dict,
        face_idx: int
    ) -> Optional[Dict]:
        """Calculate match between face and profile with enhanced scoring"""
        if not profile_encodings:
            return None
        
        try:
            # Calculate distances to all profile encodings
            distances = face_recognition.face_distance(profile_encodings, face_data.encoding)
            
            if len(distances) == 0:
                return None
            
            min_distance = np.min(distances)
            best_match_idx = np.argmin(distances)
            
            # Enhanced confidence calculation
            base_confidence = max(0.0, 1.0 - min_distance)
            
            # Apply face quality weighting
            quality_weighted_confidence = base_confidence * face_data.confidence
            
            # Stricter threshold for final acceptance
            threshold = self.analysis_config.min_confidence_threshold
            
            if quality_weighted_confidence >= threshold:
                return {
                    "input_face_index": face_idx,
                    "profile_face_index": int(best_match_idx),
                    "username": profile_info.get("username"),
                    "profile_url": profile_info.get("profile_url"),
                    "confidence": float(quality_weighted_confidence),
                    "raw_confidence": float(base_confidence),
                    "face_quality": float(face_data.confidence),
                    "distance": float(min_distance),
                    "platform": profile_info.get("platform")
                }
            
            return None
            
        except Exception as e:
            logger.debug(f"Face matching failed: {e}")
            return None
    
    def _deduplicate_matches(self, matches: List[Dict]) -> List[Dict]:
        """Remove duplicate matches based on username and confidence"""
        if not matches:
            return matches
        
        # Group by username/platform
        user_matches = {}
        for match in matches:
            key = (match.get('username'), match.get('platform'))
            if key not in user_matches:
                user_matches[key] = []
            user_matches[key].append(match)
        
        # Keep only the best match per user
        deduplicated = []
        for user_match_list in user_matches.values():
            best_match = max(user_match_list, key=lambda x: x['confidence'])
            deduplicated.append(best_match)
        
        return sorted(deduplicated, key=lambda x: x['confidence'], reverse=True)
    
    def __del__(self):
        """Cleanup resources"""
        if hasattr(self, 'thread_pool'):
            self.thread_pool.shutdown(wait=False)
