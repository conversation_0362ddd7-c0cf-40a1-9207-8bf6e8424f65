import requests
import sys
import os

def test_instagram_api_search(query="Virat <PERSON>hl<PERSON>"):
    print(f"Testing Instagram API with query: '{query}'...")

    headers = {
        "x-rapidapi-host": "instagram-scraper-stable-api.p.rapidapi.com",
        "x-rapidapi-key": "**************************************************"
    }

    # Based on the curl example, this API uses PHP endpoints with GET requests
    # Try different possible PHP endpoints for search functionality
    search_endpoints = [
        f"https://instagram-scraper-stable-api.p.rapidapi.com/search_users.php?query={query}",
        f"https://instagram-scraper-stable-api.p.rapidapi.com/search_user.php?username={query}",
        f"https://instagram-scraper-stable-api.p.rapidapi.com/get_user_search.php?query={query}",
        f"https://instagram-scraper-stable-api.p.rapidapi.com/user_search.php?q={query}",
        f"https://instagram-scraper-stable-api.p.rapidapi.com/search.php?query={query}",
        f"https://instagram-scraper-stable-api.p.rapidapi.com/find_users.php?name={query}",
        f"https://instagram-scraper-stable-api.p.rapidapi.com/get_users.php?search={query}",
    ]

    for url in search_endpoints:
        print(f"Trying endpoint: {url}")
        success = test_php_endpoint(url, headers, query)
        if success:
            return True

    print("✗ All search endpoints failed")
    print("⚠ The instagram-scraper-stable-api.p.rapidapi.com API may not have search functionality")
    print("⚠ Consider using the working rocketapi-for-developers.p.rapidapi.com API instead")
    return False

def test_php_endpoint(url, headers, query):
    try:
        print(f"  Sending GET request to: {url}")
        response = requests.get(url, headers=headers, timeout=10)
        print(f"  Response status code: {response.status_code}")

        if response.status_code == 200:
            try:
                response_data = response.json()
                print("  ✓ Successfully retrieved JSON data from Instagram API")

                # Try different response structures for PHP endpoints
                users = []
                if 'users' in response_data:
                    users = response_data['users']
                elif 'data' in response_data:
                    users = response_data['data']
                elif 'results' in response_data:
                    users = response_data['results']
                elif isinstance(response_data, list):
                    users = response_data
                elif 'response' in response_data:
                    users = response_data['response']

                print(f"  ✓ Found {len(users)} users in response")

                if len(users) == 0:
                    print("  ⚠ Warning: No users found for the search query")
                    return True  # Still consider this a successful API call

                # Extract and print user information with validation
                valid_users = 0
                for i, user in enumerate(users[:3]):  # Show only first 3 users for testing
                    # Handle different user data structures
                    user_data = user.get('user', user) if isinstance(user, dict) and 'user' in user else user

                    if isinstance(user_data, dict):
                        username = user_data.get('username', user_data.get('user_name', 'N/A'))
                        full_name = user_data.get('full_name', user_data.get('name', 'N/A'))
                        is_verified = user_data.get('is_verified', user_data.get('verified', False))

                        print(f"  User {i+1}:")
                        print(f"    Username: {username}")
                        print(f"    Full Name: {full_name}")
                        print(f"    Verified: {'✓' if is_verified else '✗'}")

                        if username != 'N/A':
                            valid_users += 1
                    else:
                        print(f"  User {i+1}: {user}")
                        valid_users += 1

                print(f"  ✓ Successfully processed {valid_users} valid users")
                print(f"  ✓ WORKING ENDPOINT FOUND: {url}")
                return True
            except ValueError:
                print("  ✗ Response is not valid JSON")
                print(f"  Response content: {response.text[:200]}...")
                return False
        else:
            print(f"  ✗ Error: Received status code {response.status_code}")
            if response.status_code == 404:
                print(f"  ✗ Endpoint not found")
            else:
                print(f"  Response content: {response.text[:200]}...")
            return False
    except requests.exceptions.Timeout:
        print("  ✗ Error: Request timed out")
        return False
    except requests.exceptions.RequestException as e:
        print(f"  ✗ Error: {e}")
        return False
    except Exception as e:
        print(f"  ✗ Unexpected error: {e}")
        return False

def test_working_api_fallback(query="Virat Kohli"):
    """Test the known working RocketAPI as a fallback."""
    print(f"\n🔄 Testing fallback API with query: '{query}'...")

    url = "https://rocketapi-for-developers.p.rapidapi.com/instagram/search"
    headers = {
        "Content-Type": "application/json",
        "x-rapidapi-host": "rocketapi-for-developers.p.rapidapi.com",
        "x-rapidapi-key": "**************************************************"
    }
    data = {"query": query}

    try:
        print(f"  Sending POST request to working API...")
        response = requests.post(url, headers=headers, json=data, timeout=15)
        print(f"  Response status code: {response.status_code}")

        if response.status_code == 200:
            response_data = response.json()
            users = response_data.get('response', {}).get('body', {}).get('users', [])
            print(f"  ✓ Successfully found {len(users)} users")

            if len(users) > 0:
                user = users[0]['user']
                print(f"  ✓ Sample result: {user.get('username')} - {user.get('full_name')}")

            return True
        else:
            print(f"  ✗ Fallback API also failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ✗ Fallback API error: {e}")
        return False

def run_all_tests():
    """Run all Instagram API tests."""
    print("🔍 Testing current API configuration...")

    test_queries = ["Virat Kohli"]
    current_api_works = False

    for i, query in enumerate(test_queries, 1):
        print(f"\n--- Test {i}/{len(test_queries)} ---")
        success = test_instagram_api_search(query)
        if success:
            current_api_works = True
        print(f"Test {i} {'✓ PASSED' if success else '✗ FAILED'}")

    if not current_api_works:
        print("\n" + "="*60)
        print("🔄 TESTING FALLBACK API")
        print("="*60)
        fallback_works = test_working_api_fallback("Virat Kohli")

        if fallback_works:
            print("\n" + "="*60)
            print("📋 RECOMMENDATION")
            print("="*60)
            print("✓ The RocketAPI (rocketapi-for-developers.p.rapidapi.com) is working")
            print("✗ The current API (instagram-scraper-stable-api.p.rapidapi.com) doesn't support search")
            print("\n🔧 To fix this issue:")
            print("1. Update config.json to use the working RocketAPI configuration")
            print("2. Or find the correct search endpoint for instagram-scraper-stable-api")
            print("3. The instagram-scraper-stable-api may be designed for post scraping, not user search")
            return False

    return current_api_works

if __name__ == "__main__":
    print("=" * 60)
    print("Instagram API Test Suite".center(60))
    print("=" * 60)
    print()

    success = run_all_tests()

    print()
    print("=" * 60)
    if success:
        print("✓ ALL TESTS PASSED!".center(60))
        print("The Instagram API is working correctly.".center(60))
        sys.exit(0)
    else:
        print("✗ SOME TESTS FAILED!".center(60))
        print("There was an issue with the Instagram API.".center(60))
        sys.exit(1)
    print("=" * 60)
