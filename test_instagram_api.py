import requests
import sys
import os

def test_instagram_api():
    print("Testing Instagram API...")
    url = "https://rocketapi-for-developers.p.rapidapi.com/instagram/search"
    
    # Data to be sent in the POST request
    data = {"query": "cristano"}
    
    headers = {
        "Content-Type": "application/json",
        "x-rapidapi-host": "rocketapi-for-developers.p.rapidapi.com",
        "x-rapidapi-key": "**************************************************"
    }
    
    try:
        print(f"Sending POST request with data: {data}")
        response = requests.post(url, headers=headers, json=data, timeout=10)
        print(f"Response status code: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"Successfully retrieved data: {response_data}")
            
            # Extract and print user information
            users = response_data['response']['body']['users']
            for user in users:
                username = user['user']['username']
                full_name = user['user']['full_name']
                profile_pic_url = user['user']['profile_pic_url']
                print(f"Username: {username}, Full Name: {full_name}, Profile Picture: {profile_pic_url}")
                
            return True
        else:
            print(f"Error: Received status code {response.status_code}")
            print(f"Response content: {response.text}")  # Print response content for more details
            return False
    except requests.exceptions.Timeout:
        print("Error: Request timed out")
        return False
    except requests.exceptions.RequestException as e:
        print(f"Error: {e}")
        return False
    except Exception as e:
        print(f"Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("Starting Instagram API test...")
    success = test_instagram_api()
    
    if success:
        print("Test passed!")
        sys.exit(0)
    else:
        print("Test failed.")
        sys.exit(1)
