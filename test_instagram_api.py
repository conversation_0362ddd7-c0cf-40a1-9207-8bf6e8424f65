import requests
import sys
import os

def test_instagram_api_search(query="Virat <PERSON>"):
    print(f"Testing Instagram API with query: '{query}'...")

    headers = {
        "Content-Type": "application/json",
        "x-rapidapi-host": "instagram-scraper-stable-api.p.rapidapi.com",
        "x-rapidapi-key": "**************************************************"
    }

    # First, let's check what endpoints are available
    print("Checking available endpoints...")
    try:
        response = requests.get("https://instagram-scraper-stable-api.p.rapidapi.com/", headers=headers, timeout=10)
        print(f"Root endpoint status: {response.status_code}")
        if response.status_code == 200:
            print(f"Root response: {response.text[:500]}")
    except Exception as e:
        print(f"Root endpoint check failed: {e}")

    # Try different possible endpoints and methods
    test_configs = [
        # Different endpoints with POST
        {"url": "https://instagram-scraper-stable-api.p.rapidapi.com/search", "method": "POST", "data": {"query": query}},
        {"url": "https://instagram-scraper-stable-api.p.rapidapi.com/user/search", "method": "POST", "data": {"query": query}},
        {"url": "https://instagram-scraper-stable-api.p.rapidapi.com/users/search", "method": "POST", "data": {"query": query}},
        # Try with different parameter names
        {"url": "https://instagram-scraper-stable-api.p.rapidapi.com/search", "method": "POST", "data": {"username": query}},
        {"url": "https://instagram-scraper-stable-api.p.rapidapi.com/search", "method": "POST", "data": {"name": query}},
        # Try GET requests with query parameters
        {"url": f"https://instagram-scraper-stable-api.p.rapidapi.com/search?query={query}", "method": "GET", "data": None},
        {"url": f"https://instagram-scraper-stable-api.p.rapidapi.com/user/search?username={query}", "method": "GET", "data": None},
    ]

    for config in test_configs:
        print(f"Trying {config['method']} {config['url']}")
        success = test_single_endpoint_with_method(config['url'], headers, config['data'], config['method'], query)
        if success:
            return True

    print("✗ All endpoint configurations failed")
    print("⚠ The instagram-scraper-stable-api.p.rapidapi.com API may not support search functionality")
    print("⚠ Consider using the working rocketapi-for-developers.p.rapidapi.com API instead")
    return False

def test_single_endpoint(url, headers, data, query):
    try:
        print(f"  Sending POST request with data: {data}")
        response = requests.post(url, headers=headers, json=data, timeout=10)
        print(f"  Response status code: {response.status_code}")

        if response.status_code == 200:
            response_data = response.json()
            print("  ✓ Successfully retrieved data from Instagram API")

            # Try different response structures
            users = []
            if 'response' in response_data and 'body' in response_data['response']:
                users = response_data['response']['body'].get('users', [])
            elif 'users' in response_data:
                users = response_data['users']
            elif 'data' in response_data:
                users = response_data['data']
            elif isinstance(response_data, list):
                users = response_data

            print(f"  ✓ Found {len(users)} users in response")

            if len(users) == 0:
                print("  ⚠ Warning: No users found for the search query")
                return True  # Still consider this a successful API call

            # Extract and print user information with validation
            valid_users = 0
            for i, user in enumerate(users[:3]):  # Show only first 3 users for testing
                # Handle different user data structures
                user_data = user.get('user', user) if 'user' in user else user

                username = user_data.get('username', 'N/A')
                full_name = user_data.get('full_name', user_data.get('name', 'N/A'))
                is_verified = user_data.get('is_verified', False)

                print(f"  User {i+1}:")
                print(f"    Username: {username}")
                print(f"    Full Name: {full_name}")
                print(f"    Verified: {'✓' if is_verified else '✗'}")

                if username != 'N/A':
                    valid_users += 1

            print(f"  ✓ Successfully processed {valid_users} valid users")
            print(f"  ✓ WORKING ENDPOINT FOUND: {url}")
            return True
        else:
            print(f"  ✗ Error: Received status code {response.status_code}")
            if response.status_code == 404:
                print(f"  ✗ Endpoint not found: {response.text}")
            else:
                print(f"  Response content: {response.text}")
            return False
    except requests.exceptions.Timeout:
        print("  ✗ Error: Request timed out")
        return False
    except requests.exceptions.RequestException as e:
        print(f"  ✗ Error: {e}")
        return False
    except Exception as e:
        print(f"  ✗ Unexpected error: {e}")
        return False

def run_all_tests():
    """Run all Instagram API tests."""
    test_queries = ["Virat Kohli", "Cristiano Ronaldo"]
    all_passed = True

    for i, query in enumerate(test_queries, 1):
        print(f"\n--- Test {i}/{len(test_queries)} ---")
        success = test_instagram_api_search(query)
        if not success:
            all_passed = False
        print(f"Test {i} {'✓ PASSED' if success else '✗ FAILED'}")

    return all_passed

if __name__ == "__main__":
    print("=" * 60)
    print("Instagram API Test Suite".center(60))
    print("=" * 60)
    print()

    success = run_all_tests()

    print()
    print("=" * 60)
    if success:
        print("✓ ALL TESTS PASSED!".center(60))
        print("The Instagram API is working correctly.".center(60))
        sys.exit(0)
    else:
        print("✗ SOME TESTS FAILED!".center(60))
        print("There was an issue with the Instagram API.".center(60))
        sys.exit(1)
    print("=" * 60)
