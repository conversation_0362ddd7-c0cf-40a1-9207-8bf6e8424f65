import requests
import sys
import os

def test_instagram_api_search(query="Virat Kohl<PERSON>"):
    print(f"Testing Instagram API with query: '{query}'...")
    # Use the same API configuration as the working instagram_search.py
    url = "https://instagram-scraper-stable-api.p.rapidapi.com/instagram"

    # Data to be sent in the POST request
    data = {"query": query}

    headers = {
        "Content-Type": "application/json",
        "x-rapidapi-host": "instagram-scraper-stable-api.p.rapidapi.com",
        "x-rapidapi-key": "**************************************************"
    }
    
    try:
        print(f"Sending POST request with data: {data}")
        response = requests.post(url, headers=headers, json=data, timeout=15)
        print(f"Response status code: {response.status_code}")

        if response.status_code == 200:
            response_data = response.json()
            print("✓ Successfully retrieved data from Instagram API")

            # Validate response structure
            if 'response' not in response_data or 'body' not in response_data['response']:
                print("✗ Error: Invalid response structure")
                return False

            users = response_data['response']['body'].get('users', [])
            print(f"✓ Found {len(users)} users in response")

            if len(users) == 0:
                print("⚠ Warning: No users found for the search query")
                return True  # Still consider this a successful API call

            # Extract and print user information with validation
            valid_users = 0
            for i, user in enumerate(users):
                if 'user' not in user:
                    print(f"⚠ Warning: User {i+1} missing 'user' field")
                    continue

                user_data = user['user']
                username = user_data.get('username', 'N/A')
                full_name = user_data.get('full_name', 'N/A')
                profile_pic_url = user_data.get('profile_pic_url', 'N/A')
                is_verified = user_data.get('is_verified', False)

                print(f"User {i+1}:")
                print(f"  Username: {username}")
                print(f"  Full Name: {full_name}")
                print(f"  Verified: {'✓' if is_verified else '✗'}")
                print(f"  Profile Picture: {profile_pic_url}")
                print()

                if username != 'N/A':
                    valid_users += 1

            print(f"✓ Successfully processed {valid_users} valid users")
            return True
        else:
            print(f"✗ Error: Received status code {response.status_code}")
            print(f"Response content: {response.text}")
            return False
    except requests.exceptions.Timeout:
        print("Error: Request timed out")
        return False
    except requests.exceptions.RequestException as e:
        print(f"Error: {e}")
        return False
    except Exception as e:
        print(f"Unexpected error: {e}")
        return False

def run_all_tests():
    """Run all Instagram API tests."""
    test_queries = ["Virat Kohli", "Cristiano Ronaldo"]
    all_passed = True

    for i, query in enumerate(test_queries, 1):
        print(f"\n--- Test {i}/{len(test_queries)} ---")
        success = test_instagram_api_search(query)
        if not success:
            all_passed = False
        print(f"Test {i} {'✓ PASSED' if success else '✗ FAILED'}")

    return all_passed

if __name__ == "__main__":
    print("=" * 60)
    print("Instagram API Test Suite".center(60))
    print("=" * 60)
    print()

    success = run_all_tests()

    print()
    print("=" * 60)
    if success:
        print("✓ ALL TESTS PASSED!".center(60))
        print("The Instagram API is working correctly.".center(60))
        sys.exit(0)
    else:
        print("✗ SOME TESTS FAILED!".center(60))
        print("There was an issue with the Instagram API.".center(60))
        sys.exit(1)
    print("=" * 60)
