

# -*- coding: utf-8 -*-
"""
Socialsearch Pro - Unified Enterprise OSINT Solution
Zero-Trust Architecture • Async-First Design • Military-Grade Security
Facial Recognition Enhanced with Profile Picture Comparison

Required Libraries:
    - asyncio (part of standard library)
    - aiofiles
    - base64 (part of standard library)
    - hashlib (part of standard library)
    - json (part of standard library)
    - logging (part of standard library)
    - secrets (part of standard library)
    - io (part of standard library)
    - datetime (part of standard library)
    - enum (part of standard library)
    - pathlib (part of standard library)
    - typing (part of standard library)
    - httpx
    - magic (python-magic)
    - structlog
    - cryptography (for Fernet)
    - pydantic
    - tenacity
    - face_recognition (Optional, requires dlib and Pillow)
    - Pillow (Optional, required by face_recognition)
    - aiosqlite (Optional, for default simulated DB)
"""

import asyncio
import aiofiles
import base64
import hashlib
import json
import logging
import os
import secrets
import io
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import httpx
# Ensure you have python-magic installed: pip install python-magic
import magic
import structlog
from cryptography.fernet import Fernet
from pydantic import BaseModel, Field, SecretStr, field_validator, ConfigDict
from tenacity import AsyncRetrying, stop_after_attempt, wait_exponential, retry_if_exception_type

# Optional dependencies for image analysis
try:
    import face_recognition
    from PIL import Image
    FACE_RECOGNITION_AVAILABLE = True
except ImportError:
    FACE_RECOGNITION_AVAILABLE = False
    face_recognition = None
    Image = None

# Structured Logging Configuration
# Basic console logger setup for structlog output
# logging.basicConfig(level=logging.INFO, format='%(message)s') # Use this if you prefer structlog's default formatting
# Or use a more traditional formatter if structlog's JSON is too verbose for console
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(name)s: %(message)s')

structlog.configure(
    processors=[
        structlog.contextvars.merge_contextvars,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        # Use a console renderer for readability in development,
        # switch to JSONRenderer for production logging systems.
        # structlog.processors.JSONRenderer(),
        structlog.dev.ConsoleRenderer()
    ],
    wrapper_class=structlog.stdlib.BoundLogger,
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)
logger = structlog.get_logger(__name__)

if not FACE_RECOGNITION_AVAILABLE:
    logger.warning("`face_recognition` or `Pillow` library not found. Facial recognition features will be disabled.")

# Platform Enumeration
class PlatformType(str, Enum):
    FACEBOOK = "facebook"
    LINKEDIN = "linkedin"
    INSTAGRAM = "instagram"
    SNAPCHAT = "snapchat"
    TIKTOK = "tiktok"
    # Add other platforms as needed

# Security Configuration
class SecurityConfig(BaseModel):
    encryption_key: SecretStr = Field(..., min_length=32)
    key_rotation_days: int = Field(7, ge=0) # Allow 0 to disable rotation
    sanitize_inputs: bool = True # Placeholder for future sanitization logic
    request_signatures: bool = True
    audit_logging: bool = True
    max_retries: int = Field(3, ge=0, le=10) # Allow 0 retries
    max_file_size: int = Field(10_485_760, ge=1_048_576)  # 10MB minimum

    @field_validator("encryption_key")
    @classmethod
    def validate_key_length(cls, v: SecretStr) -> SecretStr:
        try:
            key_bytes = v.get_secret_value().encode()
            decoded_key = base64.urlsafe_b64decode(key_bytes)
            if len(decoded_key) != 32:
                raise ValueError("Encryption key must decode to exactly 32 bytes for Fernet.")
        except (TypeError, ValueError, base64.binascii.Error) as e:
            raise ValueError(f"Encryption key must be a valid URL-safe base64-encoded string: {e}")
        return v

    def rotate_key(self) -> SecretStr:
        """Generates and sets a new encryption key."""
        new_raw_key = secrets.token_bytes(32)
        new_key = base64.urlsafe_b64encode(new_raw_key).decode()
        self.encryption_key = SecretStr(new_key)
        logger.info("Security key rotated successfully")
        return self.encryption_key

# Image Analysis Configuration
class ImageAnalysisConfig(BaseModel):
    enable_facial_recognition: bool = Field(True)
    face_detection_model: str = Field("hog") # or "cnn"
    min_confidence_threshold: Optional[float] = Field(0.6, ge=0.0, le=1.0) # Lower value means more matches (less strict)

    @field_validator("face_detection_model")
    @classmethod
    def validate_model(cls, v):
        if v not in ["hog", "cnn"]:
            raise ValueError("face_detection_model must be 'hog' or 'cnn'")
        return v

# Network Manager with Circuit Breaker, Rate Limiting, and Retries
class NetworkManager:
    """Handles asynchronous HTTP requests with built-in resilience patterns."""
    def __init__(self, security: SecurityConfig):
        self.security = security
        self.client = httpx.AsyncClient(
            limits=httpx.Limits(max_connections=200, max_keepalive_connections=50),
            timeout=httpx.Timeout(30.0)
        )
        self.circuit_state = "closed" # states: closed, open, half-open
        self.failure_count = 0
        self.circuit_open_until: Optional[datetime] = None
        self.failure_threshold = 5 # Number of failures to trip the circuit
        self.open_state_duration_seconds = 60 # How long circuit stays open

        self.rate_limiter = asyncio.Semaphore(100) # Max 100 concurrent requests
        self.request_metrics = {"total": 0, "success": 0, "failures": 0, "latency": []}

    def _sign_request(self, headers: Dict) -> Dict:
        """Adds a simple signature and timestamp to request headers."""
        if self.security.request_signatures:
            timestamp = datetime.now().isoformat()
            # Use a portion of the key for signing - security by obscurity here,
            # a real signature would involve a shared secret/key exchange.
            # Or better, sign the actual request body/method/url.
            # This is a simple illustrative example.
            secret_part = self.security.encryption_key.get_secret_value()[:16] # Use more than 10 chars
            payload = f"{timestamp}{secret_part}"
            signature = hashlib.sha256(payload.encode()).hexdigest()
            headers.update({"X-Request-Signature": signature, "X-Timestamp": timestamp})
        return headers

    def _record_success(self, start_time: datetime) -> None:
        """Records a successful request and potentially moves circuit breaker state."""
        self.request_metrics["success"] += 1
        self.request_metrics["latency"].append((datetime.now() - start_time).total_seconds())

        # Circuit breaker logic
        if self.circuit_state == "half-open":
            logger.info("Circuit breaker closed after successful request in half-open state.")
            self.circuit_state = "closed"
            self.failure_count = 0
            self.circuit_open_until = None
        elif self.failure_count > 0:
             # Reduce failure count on success in closed state, but don't close if half-open failed
            self.failure_count = max(0, self.failure_count - 1)


    def _record_failure(self, start_time: datetime) -> None:
        """Records a failed request and potentially trips the circuit breaker."""
        self.request_metrics["failures"] += 1
        self.request_metrics["latency"].append((datetime.now() - start_time).total_seconds())

        # Circuit breaker logic
        self.failure_count += 1
        if self.circuit_state == "half-open" or (self.circuit_state == "closed" and self.failure_count >= self.failure_threshold):
            logger.warning(f"Circuit breaker tripped to OPEN state after {self.failure_count} failures.")
            self.circuit_state = "open"
            self.circuit_open_until = datetime.now() + timedelta(seconds=self.open_state_duration_seconds)


    async def secure_request(self, method: str, url: str, **kwargs) -> httpx.Response:
        """
        Performs a signed, rate-limited, retried, and circuit-breaker protected HTTP request.
        Raises httpx.RequestError or httpx.HTTPStatusError on final failure.
        """
        self.request_metrics["total"] += 1
        request_log = logger.bind(method=method, url=url)

        # Circuit breaker check
        if self.circuit_state == "open":
            if self.circuit_open_until and datetime.now() < self.circuit_open_until:
                request_log.warning("Request blocked by OPEN circuit breaker.")
                # Simulate 503 Service Unavailable response
                error_response = httpx.Response(
                    status_code=503,
                    request=httpx.Request(method, url),
                    text="Service Unavailable (Circuit Breaker Open)"
                )
                raise httpx.HTTPStatusError("Circuit breaker open", request=error_response.request, response=error_response)
            else:
                request_log.info("Circuit breaker duration elapsed. Transitioning to HALF-OPEN.")
                self.circuit_state = "half-open"
                self.failure_count = 0 # Reset failure count for half-open

        async with self.rate_limiter:
            start_time = datetime.now()
            response = None
            try:
                # Configure retries - retry on network errors, timeouts, connection errors, and 5xx/429 status codes
                async for attempt in AsyncRetrying(
                    stop=stop_after_attempt(self.security.max_retries + 1 if self.security.max_retries > 0 else 1), # +1 for the initial try
                    wait=wait_exponential(multiplier=1, min=1, max=10),
                    retry=retry_if_exception_type((httpx.NetworkError, httpx.TimeoutException, httpx.ConnectError, httpx.HTTPStatusError)),
                    # Custom callback to check for specific status codes to retry
                    retry_error_callback=lambda retry_state: request_log.warning(
                        "Retrying request", attempt=retry_state.attempt_number, error=str(retry_state.outcome.exception())
                    ),
                    reraise=True # Re-raise the final exception if retries fail
                ):
                    with attempt:
                        headers = self._sign_request(kwargs.pop("headers", {}))
                        request_log.debug("Sending request", attempt=attempt.retry_state.attempt_number)
                        response = await self.client.request(method, url, headers=headers, **kwargs)
                        request_log = request_log.bind(status_code=response.status_code)

                        # Check status codes for potential retries even if no exception is thrown
                        if response.status_code >= 500 or response.status_code == 429:
                            request_log.warning("Server error or rate limit encountered. Will retry.")
                            response.raise_for_status() # This will raise httpx.HTTPStatusError, triggering retry

                        # If circuit is half-open and request fails *after* getting a response (e.g., bad status)
                        # this will be caught by the main try/except and trip it back to open.
                        # If it succeeds here, _record_success handles transition back to closed.

                        response.raise_for_status() # Raise for 4xx client errors as well (except 429 handled above)

                        # If successful and no exception:
                        self._record_success(start_time)
                        request_log.info("Request successful")
                        return response # Success! Break out of retry loop

            except (httpx.RequestError, httpx.HTTPStatusError) as err:
                # This block catches the final exception after all retries are exhausted
                self._record_failure(start_time)
                request_log.error(
                    f"Request failed after {self.security.max_retries} retries",
                    exc_info=True,
                    exception_type=type(err).__name__,
                    exception_message=str(err)
                )
                # reraise=True in AsyncRetrying ensures the exception is re-raised
                raise err
            except Exception as e:
                 # Catch any other unexpected errors during the request process
                self._record_failure(start_time)
                request_log.error(
                    "An unexpected error occurred during request after retries",
                    exc_info=True,
                    exception_type=type(e).__name__,
                    exception_message=str(e)
                )
                raise e # Re-raise the unexpected error

    async def close(self):
        """Closes the underlying HTTP client."""
        await self.client.aclose()
        logger.info("NetworkManager client closed.")

# Platform Integrator Base Class
class PlatformIntegrator:
    """Base class for platform-specific search logic."""
    def __init__(self, config: Dict, network: NetworkManager):
        platform_name = self.__class__.__name__.lower().replace('integrator', '')
        self.platform_name = platform_name
        self.config = config
        self.network = network
        # Log config excluding sensitive parts
        log_config = {k: v for k, v in config.items() if k not in ['rapidapi_key', 'client_secret', 'client_id']}
        logger.debug(f"Initializing {platform_name} integrator", config=log_config)

    async def search(self, name: str) -> Dict:
        """Performs a search for a given name on the platform."""
        raise NotImplementedError("Subclasses must implement search")

# Platform Integrators
class FacebookIntegrator(PlatformIntegrator):
    """Stub integrator for Facebook."""
    async def search(self, name: str) -> Dict:
        logger.warning(f"{self.platform_name.capitalize()} search requested, but no real API integration is implemented.", search_term=name)
        # Simulate potential matches structure for consistency
        return {"matches": [], "error": f"{self.platform_name.capitalize()} API integration not implemented."}

class TikTokIntegrator(PlatformIntegrator):
    """Stub integrator for TikTok."""
    async def search(self, name: str) -> Dict:
        logger.warning(f"{self.platform_name.capitalize()} search requested, but no real API integration is implemented.", search_term=name)
        # Simulate potential matches structure
        return {"matches": [], "error": f"{self.platform_name.capitalize()} API integration not implemented."}

class InstagramIntegrator(PlatformIntegrator):
    """Integrator for Instagram using a RapidAPI endpoint."""
    async def search(self, name: str) -> Dict:
        api_type = self.config.get('api_type')
        logger.info(f"{self.platform_name.capitalize()} search initiated", search_term=name, api_type=api_type)

        if api_type == 'rapidapi':
            user_search_url = self.config.get('api_endpoint')
            rapidapi_host = self.config.get('rapidapi_host')
            rapidapi_key_secret = self.config.get('rapidapi_key') # This comes as SecretStr from config model

            # Validate required config for RapidAPI
            if not user_search_url or not rapidapi_host or not rapidapi_key_secret:
                logger.error(f"{self.platform_name.capitalize()} RapidAPI configuration (endpoint, host, key) missing.")
                return {"matches": [], "error": f"{self.platform_name.capitalize()} API configuration incomplete."}

            rapidapi_key = rapidapi_key_secret.get_secret_value() if isinstance(rapidapi_key_secret, SecretStr) else rapidapi_key_secret # Get value from SecretStr
            if not rapidapi_key or rapidapi_key == "YOUR_RAPIDAPI_KEY_HERE":
                 logger.error(f"{self.platform_name.capitalize()} RapidAPI key is missing or is the placeholder.")
                 return {"matches": [], "error": f"{self.platform_name.capitalize()} API key missing or invalid."}


            headers = {
                'x-rapidapi-host': rapidapi_host,
                'x-rapidapi-key': rapidapi_key,
                'Content-Type': 'application/json' # RocketAPI endpoint might require this
            }
            payload = {'query': name}
            logger.debug(f"Sending POST request to {self.platform_name.capitalize()} API", url=user_search_url, payload=payload)

            try:
                # Use the secure_request method which handles retries/circuit breaker
                response = await self.network.secure_request("POST", user_search_url, headers=headers, json=payload)

                # secure_request raises exceptions on failure, so we only process successful responses here
                user_data = response.json()
                logger.debug(f"{self.platform_name.capitalize()} API response received", response_status=response.status_code, raw_response=user_data)

                matches = []
                potential_users = []
                try:
                    # Assuming RocketAPI structure: response -> body -> users -> list of { user: { ... } }
                    potential_users = user_data.get('response', {}).get('body', {}).get('users', [])
                    if not potential_users:
                        logger.info(f"No users found in {self.platform_name.capitalize()} API response for query: {name}")
                        # Even if no users, report success in communicating with API
                        return {"matches": [], "error": "No users found matching query." if user_data else "API response was empty or invalid."}

                    logger.info(f"Processing {len(potential_users)} potential users from {self.platform_name.capitalize()} API.")

                    # Filter and process users
                    for user_entry in potential_users:
                        user_info = user_entry.get('user', {})
                        username = user_info.get('username')
                        user_id = user_info.get('pk') or user_info.get('id') or username # Use pk or id or username as fallback ID
                        full_name = user_info.get('full_name', '')
                        is_verified = user_info.get('is_verified', False)
                        follower_count = user_info.get('follower_count', 0) or 0 # Handle potential None/zero values

                        log_user = logger.bind(platform=self.platform_name, potential_username=username, potential_full_name=full_name, verified=is_verified, followers=follower_count)
                        log_user.debug("Processing potential user entry.")

                        if not username:
                            log_user.debug("Skipping user due to missing username.")
                            continue # Skip if username is missing, as URL depends on it

                        # Name match filter (case-insensitive with fuzzy matching)
                        search_name_lower = name.lower()
                        full_name_lower = full_name.lower()
                        username_lower = username.lower()

                        # Check for exact match first
                        exact_match = search_name_lower in full_name_lower or search_name_lower in username_lower

                        # Check for fuzzy match (handle common misspellings)
                        fuzzy_match = False
                        if not exact_match:
                            # Handle common variations like "cristano" vs "cristiano"
                            search_words = search_name_lower.split()
                            for search_word in search_words:
                                # Check if search word is a substring of any word in full name or username
                                full_name_words = full_name_lower.split()
                                username_words = username_lower.split()

                                for word in full_name_words + username_words:
                                    # Allow partial matches if the search word is at least 4 chars and matches 80% of a word
                                    if len(search_word) >= 4 and len(word) >= 4:
                                        if search_word in word or word in search_word:
                                            fuzzy_match = True
                                            break
                                        # Check for similar words (e.g., "cristano" matches "cristiano")
                                        if abs(len(search_word) - len(word)) <= 2:
                                            # Simple character difference check
                                            common_chars = sum(1 for a, b in zip(search_word, word) if a == b)
                                            similarity = common_chars / max(len(search_word), len(word))
                                            if similarity >= 0.7:  # 70% similarity threshold
                                                fuzzy_match = True
                                                break
                                if fuzzy_match:
                                    break

                        if not exact_match and not fuzzy_match:
                             log_user.info(f"Skipping user due to name mismatch: '{username}' (full_name: '{full_name}')")
                             continue # Skip if name doesn't appear in full_name or username

                        # Relevance filter: Prioritize verified accounts or high follower counts (e.g., >100)
                        # Made less strict to show more results
                        if not is_verified and follower_count < 100:
                            log_user.info(f"Skipping low-relevance account: '{username}' (verified: {is_verified}, followers: {follower_count})")
                            continue # Skip low relevance accounts unless verified

                        # If filters pass, add to matches
                        match = {
                            "id": str(user_id), # Ensure ID is string
                            "name": full_name if full_name else username, # Prefer full name if available
                            "username": username,
                            "platform": self.platform_name,
                            "profile_url": f"https://www.instagram.com/{username}", # URL generated here
                            "is_verified": is_verified,
                            "followers": follower_count,
                            "following": user_info.get('following_count'), # Use .get to handle missing key
                            "posts": user_info.get('media_count'),       # Use .get to handle missing key
                            "bio": user_info.get('biography', ''),
                            "profile_pic_url": user_info.get('profile_pic_url', None)
                        }
                        # Clean up None values before appending (optional, Pydantic models handle None fine)
                        match = {k: v for k, v in match.items() if v is not None}
                        matches.append(match)
                        log_user.debug("Added potential user as a match.")


                    if len(matches) > 5:
                        logger.info(f"Multiple relevant matches ({len(matches)}) found for '{name}'. Consider refining the search term.")
                    elif len(matches) > 0:
                         logger.info(f"Found {len(matches)} relevant {self.platform_name.capitalize()} user(s) for query: {name}")
                    else:
                         logger.info(f"Found 0 relevant {self.platform_name.capitalize()} user(s) after filtering for query: {name}")

                    return {"matches": matches}

                except (KeyError, TypeError) as e:
                    logger.error(f"Failed to parse {self.platform_name.capitalize()} API response format: {str(e)}", exc_info=True, response_keys=list(user_data.keys()) if isinstance(user_data, dict) else type(user_data))
                    return {"matches": [], "error": f"Invalid API response format: {str(e)}"}

            except httpx.HTTPStatusError as http_err:
                error_detail = f"{self.platform_name.capitalize()} API HTTP error: {http_err.response.status_code}"
                try:
                    error_detail += f" - {http_err.response.text[:200]}"
                except Exception: # Handle cases where response.text might fail
                    pass
                logger.error(error_detail, exc_info=True)
                return {"matches": [], "error": error_detail}
            except httpx.RequestError as req_err:
                 logger.error(f"{self.platform_name.capitalize()} API Network or Request error: {str(req_err)}", exc_info=True)
                 return {"matches": [], "error": f"{self.platform_name.capitalize()} API Network or Request error: {str(req_err)}"}
            except Exception as api_error:
                logger.error(f"{self.platform_name.capitalize()} RapidAPI request failed unexpectedly", exc_info=True)
                return {"matches": [], "error": f"{self.platform_name.capitalize()} API request failed: {str(api_error)}"}
        else:
            logger.warning(f"{self.platform_name.capitalize()} search skipped: Configured api_type ('{api_type}') is not 'rapidapi'.")
            return {"matches": [], "error": f"{self.platform_name.capitalize()} API not configured or unsupported type."}


# Async Database (Simulated)
class AsyncDatabase:
    """Simulates asynchronous database operations for audit logging."""
    def __init__(self, dsn: str):
        self.dsn = dsn # Data Source Name, e.g., connection string
        self.connected = False
        # In a real app, this would be an async DB connection pool
        # self._pool = None

    async def connect(self):
        """Simulates connecting to the database."""
        try:
            logger.info("Attempting database connection (simulated)...", dsn=self.dsn)
            await asyncio.sleep(0.05) # Simulate connection time
            self.connected = True
            logger.info("Database connection established (simulated)")
            # In real app: self._pool = await asyncpg.create_pool(...)
            # Or for aiosqlite: self._db = await aiosqlite.connect(self.dsn.replace("sqlite+aiosqlite:///", ""))
            # await self._db.execute("CREATE TABLE IF NOT EXISTS audit_logs (...)") etc.

        except Exception as e:
            logger.error("Database connection failed (simulated)", dsn=self.dsn, error=str(e), exc_info=True)
            self.connected = False

    async def disconnect(self):
        """Simulates disconnecting from the database."""
        if self.connected:
            logger.info("Closing database connection (simulated)...")
            await asyncio.sleep(0.01) # Simulate disconnect time
            self.connected = False
            logger.info("Database connection closed (simulated)")
            # In real app: await self._pool.close() or await self._db.close()

    async def log_audit(self, event: str, details: Dict):
        """Simulates writing an audit log entry."""
        if self.connected:
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "event": event,
                "details": details
            }
            try:
                # In a real app, you'd insert into a DB table:
                # async with self._pool.acquire() as connection:
                #     await connection.execute("INSERT INTO audit_logs (timestamp, event, details) VALUES ($1, $2, $3)",
                #                              log_entry["timestamp"], log_entry["event"], json.dumps(log_entry["details"]))
                 # Or with aiosqlite: await self._db.execute("INSERT INTO audit_logs (timestamp, event, details) VALUES (?, ?, ?)", (log_entry["timestamp"], log_entry["event"], json.dumps(log_entry["details"])))
                 # await self._db.commit()
                await asyncio.sleep(0.005) # Simulate write time
                # Print audit log for simulation visibility
                logger.debug("Audit event logged to DB (simulated)", audit_event_data=log_entry)
            except Exception as e:
                # Log failure, but don't block main operation
                logger.error("Failed to write audit log to DB (simulated)", error=str(e), audit_event_name=event, exc_info=True)
        else:
            # In a real app, you might queue these logs or log to stderr
            logger.warning("Audit log skipped: Database not connected", audit_event_name=event)

# Image Analysis Engine
class ImageAnalysisEngine:
    """Handles image processing and facial recognition."""
    def __init__(self, security: SecurityConfig, analysis_config: ImageAnalysisConfig):
        self.security = security
        self.analysis_config = analysis_config
        self.network = NetworkManager(security) # Use a separate network manager for image downloads
        self.fernet: Optional[Fernet] = None
        self._init_fernet()

        # Initialize python-magic
        try:
            self.mime = magic.Magic(mime=True)
        except Exception as e:
            logger.error("Failed to initialize python-magic. File type checks may be inaccurate.", exc_info=True)
            # Fallback or disable file type check? For now, let it potentially fail later.

        self._check_dependencies()

    def _init_fernet(self):
        """Initializes the Fernet cipher."""
        try:
            # Ensure the key is bytes for Fernet
            key_bytes = base64.urlsafe_b64decode(self.security.encryption_key.get_secret_value().encode())
            if len(key_bytes) != 32:
                 # This should have been caught by the pydantic validator, but double-check
                 raise ValueError("Decoded encryption key must be 32 bytes long for Fernet.")
            self.fernet = Fernet(self.security.encryption_key.get_secret_value().encode())
            logger.debug("Fernet cipher initialized.")
        except (TypeError, ValueError, base64.binascii.Error) as e:
            logger.error("Failed to initialize Fernet cipher.", exc_info=True)
            self.fernet = None # Disable encryption/decryption features
            logger.warning("Image encryption/decryption features disabled due to invalid key.")
        except Exception as e:
             logger.error("Unexpected error during Fernet initialization.", exc_info=True)
             self.fernet = None
             logger.warning("Image encryption/decryption features disabled due to unexpected error.")


    def _check_dependencies(self):
        """Checks if facial recognition dependencies are available and updates config."""
        if self.analysis_config.enable_facial_recognition and not FACE_RECOGNITION_AVAILABLE:
            logger.error("Facial recognition enabled in config, but `face_recognition` or `Pillow` dependencies are missing!")
            self.analysis_config.enable_facial_recognition = False
            logger.warning("Disabling facial recognition feature.")

    async def analyze(self, photo_path: Path, profile_pics: List[Dict] = None) -> Dict:
        """Analyzes a local photo, performs facial recognition, and compares to profile pictures."""
        log = logger.bind(photo_path=str(photo_path))
        analysis_result = {
            "status": "pending",
            "file_path": str(photo_path),
            "file_type": None,
            "size_bytes": None,
            "error": None,
            "facial_recognition_enabled_in_config": self.analysis_config.enable_facial_recognition,
            "facial_recognition_actually_run": False,
            "face_count": 0,
            "face_locations": [], # Optional: store locations if needed
            "face_encodings": [], # Sensitive: consider encrypting or hashing if stored long-term
            "profile_pic_matches": []
        }

        try:
            if not await asyncio.to_thread(photo_path.exists):
                raise FileNotFoundError(f"Image file not found: '{photo_path}'")

            stat_result = await asyncio.to_thread(photo_path.stat)
            analysis_result["size_bytes"] = stat_result.st_size
            if analysis_result["size_bytes"] > self.security.max_file_size:
                raise ValueError(f"File size ({analysis_result['size_bytes']} bytes) exceeds configured limit ({self.security.max_file_size} bytes).")

            # Perform mime type check asynchronously
            try:
                file_type = await asyncio.to_thread(self.mime.from_file, str(photo_path))
                analysis_result["file_type"] = file_type
                if not file_type or not file_type.startswith("image/"):
                    raise ValueError(f"Invalid file type: '{file_type}'. Expected an image (image/*).")
                log.info("Image validated", type=file_type, size=analysis_result["size_bytes"])
            except Exception as mime_err:
                 # Log error but proceed if possible, might be a false negative
                 log.warning("Could not determine or validate file type using python-magic.", error=str(mime_err))
                 analysis_result["file_type"] = "unknown/possibly_image" # Mark as potentially image

            async with aiofiles.open(photo_path, "rb") as f:
                image_content = await f.read()

            # Optional: Decrypt image content if stored encrypted (not implemented here, but possible)
            # if self.fernet and is_encrypted(image_content):
            #     image_content = self.fernet.decrypt(image_content)

            if self.analysis_config.enable_facial_recognition and FACE_RECOGNITION_AVAILABLE:
                analysis_result["facial_recognition_actually_run"] = True
                log.debug("Starting facial recognition on input photo", model=self.analysis_config.face_detection_model)
                try:
                    # Perform face recognition on the input photo
                    face_locations, face_encodings = await asyncio.to_thread(
                        self._perform_face_recognition,
                        image_content,
                        self.analysis_config.face_detection_model
                    )
                    analysis_result["face_count"] = len(face_locations)
                    analysis_result["face_locations"] = face_locations
                    # Store encodings as lists for JSON serialization
                    analysis_result["face_encodings"] = [enc.tolist() for enc in face_encodings]
                    log.info(f"Facial recognition on input photo completed. Found {len(face_locations)} face(s).")

                    # Compare with profile pictures if provided and input faces were found
                    if profile_pics and face_encodings:
                        log.debug(f"Starting comparison with {len(profile_pics)} profile pictures.")
                        analysis_result["profile_pic_matches"] = await self._compare_with_profile_pics(
                            face_encodings, profile_pics, log
                        )
                        log.info(f"Profile picture comparison completed. Found {len(analysis_result['profile_pic_matches'])} matches.")

                except Exception as fr_err:
                    log.error("Facial recognition process failed for input photo.", exc_info=True)
                    analysis_result["error"] = f"Facial recognition error: {str(fr_err)}"
                    # Set status to failed_analysis if FR failed but file was okay
                    analysis_result["status"] = "failed_analysis"
                    # Keep other successful checks (size, type)


            # Set status to analyzed if no error occurred
            if analysis_result.get("status") == "pending":
                analysis_result["status"] = "analyzed"

        except FileNotFoundError as e:
            log.error("Image analysis error: File not found", error=str(e))
            analysis_result["status"] = "error"
            analysis_result["error"] = str(e)
        except ValueError as e:
            log.error(f"Image analysis error: Validation failed", error=str(e))
            analysis_result["status"] = "error"
            analysis_result["error"] = str(e)
        except Exception as e:
            log.error("Unexpected error during image analysis pipeline.", exc_info=True)
            analysis_result["status"] = "error"
            analysis_result["error"] = f"Unexpected analysis error: {str(e)}"

        # Ensure face_encodings are cleared if status is error or failed_analysis for security/privacy
        if analysis_result["status"] in ["error", "failed_analysis"]:
             analysis_result["face_encodings"] = [] # Do not return raw encodings on failure

        return analysis_result

    async def _compare_with_profile_pics(self, input_encodings: List, profile_pics: List[Dict], log: structlog.BoundLogger) -> List[Dict]:
        """Compares input face encodings against faces found in downloaded profile pictures."""
        matches = []
        comparison_log = log.bind(task="profile_pic_comparison")

        async def process_profile(profile):
            """Helper async function to process a single profile picture."""
            username = profile.get("username", "unknown_user")
            profile_pic_url = profile.get("profile_pic_url")
            profile_log = comparison_log.bind(profile_username=username, profile_url=profile.get("profile_url"))

            if not profile_pic_url:
                profile_log.debug("No profile picture URL provided, skipping comparison for this profile.")
                return None # No URL, nothing to process

            profile_log.debug("Processing profile picture for comparison.")
            try:
                # Download profile picture using the NetworkManager
                response = await self.network.secure_request("GET", profile_pic_url)
                # secure_request raises exceptions on failure, so a successful response is guaranteed here

                pic_content = response.content
                pic_type = self.mime.from_buffer(pic_content)

                if not pic_type.startswith("image/"):
                    profile_log.warning(f"Invalid profile picture type: {pic_type}, skipping comparison.")
                    return None

                # Perform facial recognition on the profile picture
                profile_locations, profile_encodings = await asyncio.to_thread(
                    self._perform_face_recognition, pic_content, self.analysis_config.face_detection_model
                )

                if not profile_encodings:
                    profile_log.debug("No faces found in profile picture.")
                    return None # No faces in profile pic, no comparison possible

                # Compare input encodings against *all* faces found in this profile picture
                profile_matches = []
                for input_idx, input_enc in enumerate(input_encodings):
                    # face_recognition.compare_faces returns a list of booleans
                    # face_recognition.face_distance returns a list of distances
                    comparison_results = face_recognition.compare_faces(
                        profile_encodings, input_enc, tolerance=self.analysis_config.min_confidence_threshold
                    )
                    face_distances = face_recognition.face_distance(profile_encodings, input_enc)

                    # Find matches for this input face against faces in the profile pic
                    for profile_face_idx, is_match in enumerate(comparison_results):
                        if is_match:
                            distance = face_distances[profile_face_idx]
                            # Confidence can be inversely related to distance, e.g., 1.0 - distance
                            # Note: face_recognition distances are not strictly 0-1, so this is approximate
                            confidence = max(0.0, 1.0 - distance) # Ensure confidence is not negative

                            profile_matches.append({
                                "input_face_index": input_idx,
                                "profile_face_index": profile_face_idx,
                                "username": username,
                                "profile_url": profile.get("profile_url"),
                                "match_distance": float(distance),
                                "match_confidence_approx": float(confidence),
                                "profile_pic_url": profile_pic_url,
                                "source_platform": profile.get("platform") # Include source platform
                            })
                            profile_log.info(f"Face match found for input face {input_idx} with profile face {profile_face_idx} ({username})")

                return profile_matches # Return list of matches found within this profile pic

            except Exception as e:
                profile_log.error("Failed to process profile picture for comparison.", exc_info=True)
                return None # Indicate failure for this profile, don't block others

        # Process profiles concurrently
        tasks = [process_profile(profile) for profile in profile_pics]
        results = await asyncio.gather(*tasks, return_exceptions=False) # Let process_profile handle its own errors

        # Collect valid matches from all processed profiles
        for result in results:
            if result is not None:
                matches.extend(result)

        # Optional: Deduplicate matches if the same input face matches multiple faces in the same profile pic
        # or if the same profile pic appears multiple times.
        # The current structure allows for multiple matches per profile if multiple faces are found in both.
        # If you only want one match entry per username/profile, you'd need to aggregate/deduplicate here.

        return matches


    def _perform_face_recognition(self, image_bytes: bytes, model: str) -> Tuple[List[Tuple[int, int, int, int]], List[Any]]:
        """
        Performs face detection and encoding extraction.
        Runs in a separate thread using asyncio.to_thread.
        """
        if not FACE_RECOGNITION_AVAILABLE:
            # Should be caught by _check_dependencies and config update, but safety check
            logger.error("Face recognition requested but library not available.")
            return [], []

        try:
            # face_recognition library expects a path or a numpy array
            # Using BytesIO and PIL to load from bytes might be necessary if not saving to temp file
            # Or load from numpy array derived from bytes (e.g. via opencv or PIL)
            # face_recognition.load_image_file can take a file-like object or bytes
            # Let's try directly with bytes (needs Pillow)
            img_stream = io.BytesIO(image_bytes)
            # Use PIL to open and check format/corruptness before face_recognition
            img = Image.open(img_stream)
            # Ensure it's RGB for face_recognition
            img_rgb = img.convert("RGB")

            # face_recognition.load_image_file can work with BytesIO or Path
            # If img_rgb is already a PIL Image, convert to numpy array if load_image_file doesn't like it
            # The docstring says "Load an image file into a numpy array". Let's assume it can handle BytesIO.
            img_stream.seek(0) # Reset stream position for face_recognition
            # Using load_image_file on BytesIO *requires* Pillow
            img_array = face_recognition.load_image_file(img_stream)

            face_locations = face_recognition.face_locations(img_array, model=model)
            if not face_locations:
                logger.debug("No face locations found in image.")
                return [], []
            # Get encodings for the found locations
            face_encodings = face_recognition.face_encodings(img_array, known_face_locations=face_locations)

            if len(face_locations) != len(face_encodings):
                 logger.warning(f"Mismatch between face locations ({len(face_locations)}) and encodings ({len(face_encodings)}).")

            return face_locations, face_encodings
        except Exception as e:
            logger.error(f"Error during face detection or encoding ({model} model)", exc_info=True)
            # Re-raise as a specific analysis error
            raise RuntimeError(f"Face recognition processing failed: {e}")

# Core Application
class SocialMapperPro:
    """Main application class orchestrating search and analysis."""
    def __init__(self, config: Dict[str, Any]):
        logger.info("Initializing SocialMapperPro...")
        try:
            # Use the Pydantic models for validation
            processed_config = SocialMapperConfig(**config)
            self.security_config = processed_config.security
            self.image_analysis_config = processed_config.image_analysis
            self.platform_configs = processed_config.platforms
            self.database_config = processed_config.database
            self.search_config = processed_config.search

        except Exception as e:
            logger.critical(f"Core configuration validation failed: {e}", exc_info=True)
            # Re-raise after logging
            raise ValueError(f"Configuration error: {e}")

        # Initialize components after successful config validation
        self.database = AsyncDatabase(self.database_config.dsn)
        self.network = NetworkManager(self.security_config)
        self.image_analyzer = ImageAnalysisEngine(self.security_config, self.image_analysis_config)
        self.integrators: Dict[str, PlatformIntegrator] = {}
        self._init_platforms()
        self._key_rotation_task: Optional[asyncio.Task] = None

        logger.info("SocialMapperPro components initialized.")

    def _init_platforms(self):
        """Initializes enabled platform integrators."""
        integrator_classes = {
            PlatformType.FACEBOOK: FacebookIntegrator,
            PlatformType.TIKTOK: TikTokIntegrator,
            PlatformType.INSTAGRAM: InstagramIntegrator,
            # Add other platform integrators here
        }
        for platform_type, platform_config in self.platform_configs.items():
            # platform_type is already a PlatformType enum member
            platform_str = platform_type.value
            if platform_config.enabled:
                if platform_type in integrator_classes:
                    cls = integrator_classes[platform_type]
                    # Pass the dictionary representation of the Pydantic config model
                    self.integrators[platform_str] = cls(platform_config.model_dump(), self.network)
                    logger.debug(f"Initialized integrator for enabled platform: {platform_str}")
                else:
                    logger.warning(f"No integrator class found for enabled platform: {platform_str}")
            else:
                logger.debug(f"Platform '{platform_str}' is disabled in configuration.")


    def _schedule_key_rotation(self):
        """Schedules the periodic security key rotation task."""
        # Cancel any existing task before scheduling a new one (e.g., if initialize is called multiple times)
        if self._key_rotation_task and not self._key_rotation_task.done():
            logger.info("Cancelling existing key rotation task.")
            self._key_rotation_task.cancel()
            # Don't await here, just let it cancel

        rotation_interval_seconds = self.security_config.key_rotation_days * 86400

        if self.security_config.key_rotation_days > 0:
            async def rotate_periodically():
                logger.info(f"Security key rotation enabled. Next rotation scheduled in {self.security_config.key_rotation_days} days.")
                while True:
                    try:
                        await asyncio.sleep(rotation_interval_seconds)
                        logger.info("Performing scheduled security key rotation...")
                        new_key = self.security_config.rotate_key()
                        # Update any components that hold the key or derived objects (like Fernet)
                        try:
                            # Re-initialize Fernet with the new key
                            self.image_analyzer._init_fernet() # Call internal method to re-init
                            logger.info("Image analysis engine Fernet cipher updated with new key.")
                        except Exception as fernet_err:
                            logger.critical("CRITICAL: Failed to update Fernet cipher key after rotation. Encryption/Decryption may fail.", exc_info=True)

                        if self.security_config.audit_logging:
                            # Pass sensitive data sanitized automatically by log_audit_event
                            await self.log_audit_event("SecurityKeyRotated", {"new_key": new_key, "rotation_interval_days": self.security_config.key_rotation_days})

                    except asyncio.CancelledError:
                        logger.info("Security key rotation task cancelled.")
                        break
                    except Exception as rotation_err:
                        logger.error("Failed during periodic security key rotation", exc_info=True)
                        if self.security_config.audit_logging:
                            await self.log_audit_event("SecurityKeyRotationFailed", {"error": str(rotation_err), "rotation_interval_days": self.security_config.key_rotation_days})
                        # Sleep for a shorter period before trying again after an error
                        await asyncio.sleep(3600) # Retry rotation attempt in 1 hour

            self._key_rotation_task = asyncio.create_task(rotate_periodically(), name="key-rotation-task")
            logger.info("Scheduled periodic security key rotation task.")
        else:
            logger.warning("Security key rotation disabled (key_rotation_days is 0).")


    async def initialize(self):
        """Connects to services and schedules background tasks."""
        logger.info("Initializing SocialMapperPro connections and tasks...")
        await self.database.connect()
        self._schedule_key_rotation()
        if self.security_config.audit_logging:
            await self.log_audit_event("SystemInitialized", {"status": "success"})
        logger.info("SocialMapperPro initialization complete.")

    async def shutdown(self):
        """Shuts down connections and cancels tasks."""
        logger.info("Shutting down SocialMapperPro...")
        # Cancel background tasks
        if self._key_rotation_task and not self._key_rotation_task.done():
            logger.info("Cancelling key rotation task...")
            self._key_rotation_task.cancel()
            try:
                await self._key_rotation_task # Wait for cancellation to complete
            except asyncio.CancelledError:
                logger.info("Key rotation task successfully cancelled.")
            except Exception as e:
                logger.error("Error awaiting key rotation task cancellation", exc_info=True)

        # Log shutdown event before disconnecting database
        # Ensure audit logging is checked, as security_config might be None if init failed early
        if hasattr(self, 'security_config') and self.security_config.audit_logging:
            try:
                await self.log_audit_event("SystemShutdown", {"status": "success"})
            except Exception as e:
                logger.error("Failed to log SystemShutdown audit event during shutdown", exc_info=True)

        # Close connections
        if hasattr(self, 'network'): # Check if network manager was initialized
            await self.network.close()
        if hasattr(self, 'database'): # Check if database was initialized
             await self.database.disconnect()


        logger.info("SocialMapperPro shutdown complete.")

    async def log_audit_event(self, event_name: str, details: Dict):
        """Logs an audit event to the database, sanitizing sensitive details."""
        # Add common details like current user/context if available in a real app
        # For this script, just timestamp and details are logged
        log_details = {"event_name": event_name, "details": details}
        # logger.debug("Attempting to log audit event", **log_details) # Too verbose normally

        # Check if audit logging is enabled and database is available
        # Check hasattr(self, 'security_config') in case log_audit_event is called before config validation
        if hasattr(self, 'security_config') and self.security_config.audit_logging:
             # Check hasattr(self, 'database') and self.database.connected in case DB init failed
             if hasattr(self, 'database') and self.database.connected:
                sanitized_details = self._sanitize_audit_details(details)
                await self.database.log_audit(event_name, sanitized_details)
             else:
                # Fallback logging if DB is configured but not connected
                logger.warning("Audit log skipped: Database not connected", audit_event_name=event_name, details=details)
        else:
            logger.debug("Audit logging disabled, skipping event", audit_event_name=event_name)


    def _sanitize_audit_details(self, details: Any) -> Any:
        """Recursively sanitizes sensitive data in dictionaries, lists, and Pydantic models."""
        sensitive_keys = {'key', 'password', 'secret', 'token', 'encryption_key', 'rapidapi_key', 'client_secret'}

        if isinstance(details, dict):
            sanitized = {}
            for k, v in details.items():
                if isinstance(k, str) and any(sens_key in k.lower() for sens_key in sensitive_keys):
                    sanitized[k] = "***REDACTED_KEY_NAME_MATCH***"
                elif isinstance(v, SecretStr):
                    sanitized[k] = "***REDACTED_SECRET_STR***"
                elif isinstance(v, (str, bytes)) and isinstance(k, str) and any(sens_key in k.lower() for sens_key in sensitive_keys):
                     # Redact string/bytes values if key name is sensitive
                     sanitized[k] = "***REDACTED_SENSITIVE_VALUE***"
                elif isinstance(v, (dict, list, BaseModel, Path)): # Recurse into nested structures
                    sanitized[k] = self._sanitize_audit_details(v)
                else:
                    # Basic types or non-sensitive keys
                    sanitized[k] = v
            return sanitized
        elif isinstance(details, list):
            return [self._sanitize_audit_details(item) for item in details]
        elif isinstance(details, BaseModel):
             # Convert Pydantic model to dict and sanitize
             return self._sanitize_audit_details(details.model_dump())
        elif isinstance(details, SecretStr):
            return "***REDACTED_SECRET_STR***"
        elif isinstance(details, Path):
             # Convert Path objects to string representation
             return str(details)
        else:
            # Return primitive types directly
            return details


    async def _search_platform(self, platform: str, name: str, query_id: str) -> Dict:
        """Executes a search on a single platform."""
        log = logger.bind(query_id=query_id, platform=platform, search_term=name)
        if platform not in self.integrators:
            log.warning("Integrator not available/enabled for platform.")
            # Audit log for skipped platform
            if self.security_config.audit_logging:
                 await self.log_audit_event("PlatformSearchSkipped", {"query_id": query_id, "platform": platform, "reason": "Not configured or disabled"})
            return {"status": "skipped", "error": f"Platform '{platform}' not configured or supported.", "matches": []} # Ensure 'matches' key is present

        log.info("Initiating platform search.")
        integrator = self.integrators[platform]
        result: Dict = {"status": "error", "matches": [], "error": "An unexpected error occurred."} # Default error state

        try:
            # Audit log before search
            if self.security_config.audit_logging:
                 await self.log_audit_event("PlatformSearchStarted", {"query_id": query_id, "platform": platform, "name": name})

            # The integrator's search method is expected to return a dict with 'matches' (list) and optional 'error' (str)
            integrator_result = await integrator.search(name)

            # Normalize integrator result structure
            result = {
                "status": "completed" if "matches" in integrator_result and not integrator_result.get("error") else "error",
                "matches": integrator_result.get("matches", []),
                "error": integrator_result.get("error")
            }
            # Ensure status is 'completed' even with 0 matches if no error occurred
            if "matches" in integrator_result and not integrator_result.get("error") and not integrator_result.get("matches"):
                 result["status"] = "completed"


            # Audit log after search
            if self.security_config.audit_logging:
                await self.log_audit_event("PlatformSearchCompleted", {
                    "query_id": query_id,
                    "platform": platform,
                    "name": name,
                    "status": result["status"],
                    "match_count": len(result.get("matches", [])),
                    "error": result.get("error")
                })

            log.info("Platform search finished.", status=result["status"], match_count=len(result.get("matches", [])), error=result.get("error"))
            return result

        except Exception as exc:
            # Catch any unexpected error during integrator execution itself
            log.error("Platform search failed unexpectedly.", exc_info=True)
            result["status"] = "error"
            result["error"] = f"An unexpected error occurred during {platform} search execution: {str(exc)}"

            if self.security_config.audit_logging:
                await self.log_audit_event("PlatformSearchFailed", {"query_id": query_id, "platform": platform, "name": name, "error": result["error"]})

            return result


    async def _analyze_image(self, photo_path: Optional[Path], platform_results: Dict, query_id: str) -> Dict:
        """Analyzes the provided image and compares faces to profile pictures from platform results."""
        log = logger.bind(query_id=query_id, photo_path=str(photo_path) if photo_path else "None")
        if photo_path is None:
            log.info("Image analysis skipped: No photo provided.")
            # Audit log for skipped analysis
            # Check security_config exists before calling log_audit_event
            if hasattr(self, 'security_config') and self.security_config.audit_logging:
                 await self.log_audit_event("ImageAnalysisSkipped", {"query_id": query_id, "reason": "No photo provided"})
            return {"status": "skipped", "reason": "No photo provided"}

        # Check if FR is enabled and deps are available, otherwise skip relevant parts
        if not self.image_analyzer.analysis_config.enable_facial_recognition:
            log.warning("Image analysis skipped: Facial recognition is disabled in configuration.")
            # Check security_config exists
            if hasattr(self, 'security_config') and self.security_config.audit_logging:
                 await self.log_audit_event("ImageAnalysisSkipped", {"query_id": query_id, "reason": "Facial recognition disabled in config"})
            # Still perform basic file checks if needed, but for now skip all analysis
            return {"status": "skipped", "reason": "Facial recognition disabled in config"}
             # Alternative: Run file validation only
             # analysis_result = {"status": "pending", "file_path": str(photo_path), ...}
             # ... perform file size/type check ...
             # analysis_result["status"] = "analyzed_file_only"
             # return analysis_result


        log.info("Initiating image analysis.")
        analysis_result: Dict = {"status": "error", "error": "An unexpected error occurred."} # Default error state

        try:
            # Check security_config exists
            if hasattr(self, 'security_config') and self.security_config.audit_logging:
                await self.log_audit_event("ImageAnalysisStarted", {"query_id": query_id, "photo_path": str(photo_path)})

            # Extract profile pictures from successful platform results
            profile_pics_to_compare = []
            for platform, result in platform_results.items():
                # Only process results from platforms that completed successfully and returned matches
                if result.get("status") == "completed" and result.get("matches"):
                    for match in result.get("matches", []):
                        # Ensure the match has a profile_pic_url
                        if match and isinstance(match, dict) and match.get("profile_pic_url"):
                             # Add enough info to identify the source of the profile pic later
                             profile_pics_to_compare.append({
                                "platform": platform,
                                "username": match.get("username", match.get("name")), # Fallback for username
                                "profile_url": match.get("profile_url"),
                                "profile_pic_url": match["profile_pic_url"]
                            })
            log.info(f"Collected {len(profile_pics_to_compare)} profile pictures for comparison from platform results.")

            # Run the image analysis engine
            analysis_result = await self.image_analyzer.analyze(photo_path, profile_pics_to_compare)

            # Audit log after analysis
            # Check security_config exists
            if hasattr(self, 'security_config') and self.security_config.audit_logging:
                await self.log_audit_event("ImageAnalysisCompleted", {
                    "query_id": query_id,
                    "photo_path": str(photo_path),
                    "status": analysis_result.get("status"),
                    "face_count": analysis_result.get("face_count"),
                    "match_count": len(analysis_result.get("profile_pic_matches", [])),
                    "error": analysis_result.get("error")
                })

            log.info("Image analysis completed.", status=analysis_result.get('status'), faces_found=analysis_result.get('face_count', 0), matches_found=len(analysis_result.get("profile_pic_matches", [])), error=analysis_result.get('error'))
            return analysis_result

        except Exception as exc:
            # Catch unexpected errors during the _analyze_image pipeline itself
            log.error("Image analysis task failed unexpectedly.", exc_info=True)
            analysis_result["status"] = "error"
            analysis_result["error"] = f"An unexpected error occurred during image analysis execution: {str(exc)}"

            # Check security_config exists
            if hasattr(self, 'security_config') and self.security_config.audit_logging:
                await self.log_audit_event("ImageAnalysisFailed", {"query_id": query_id, "photo_path": str(photo_path), "error": analysis_result["error"]})

            return analysis_result

    def _generate_summary(self, results: Dict) -> Dict:
        """Generates a summary dictionary from the analysis results."""
        total_matches = 0
        platform_summary = {}
        errors = []

        # Summarize platform results
        for platform, result in results.get("platform_results", {}).items():
            status = result.get("status", "unknown")
            platform_summary[platform] = {
                "status": status,
                "match_count": len(result.get("matches", [])) if status == "completed" else 0,
                "error": result.get("error") if status in ["error", "skipped"] else None # Include error for skipped too
            }
            if status == "completed":
                total_matches += platform_summary[platform]["match_count"]
            elif status in ["error", "skipped"]: # Also add skipped platforms to errors list for visibility
                errors.append(f"Platform '{platform}' ({status}): {result.get('error', 'Unknown error')}")

        # Summarize image analysis result
        image_analysis_result = results.get("image_analysis_result", {})
        image_status = image_analysis_result.get("status", "not_run")
        image_summary = {
            "status": image_status,
            "face_count": image_analysis_result.get("face_count", 0) if image_status in ["analyzed", "failed_analysis"] else 0,
            "face_match_count": len(image_analysis_result.get("profile_pic_matches", [])) if image_status in ["analyzed", "failed_analysis"] else 0,
            "error": image_analysis_result.get("error") if image_status in ["error", "failed_analysis", "skipped"] else None # Include error for skipped
        }
        if image_status in ["error", "failed_analysis", "skipped"]:
            errors.append(f"Image Analysis ({image_status}): {image_analysis_result.get('error', image_analysis_result.get('reason', 'Unknown error'))}")


        overall_status = "completed"
        if errors:
             overall_status = "completed_with_errors"

        # Refine overall status if critical failures occurred
        if any(ps["status"] == "error" for ps in platform_summary.values()) or \
           (results.get("query_details", {}).get("photo_provided") and image_summary["status"] in ["error", "failed_analysis"]):
             # If any requested platform failed, or requested image analysis failed
             overall_status = "failed_partially"

        # Check if *all* requested tasks failed or were skipped
        requested_platforms = set(results.get("query_details", {}).get("platforms_requested", []))
        actual_platforms_attempted = set(results.get("platform_results", {}).keys()) # These were at least considered/checked for integrators

        all_platforms_failed_or_skipped = True
        if requested_platforms: # Only check if platforms were actually requested
             for p in requested_platforms:
                 p_res = results.get("platform_results", {}).get(p)
                 if p_res and p_res.get("status") in ["completed", "pending"]: # If any requested platform completed or is pending (shouldn't be pending here)
                     all_platforms_failed_or_skipped = False
                     break
             # If we iterated through all requested platforms and didn't find a completed/pending one
             if all_platforms_failed_or_skipped:
                 # Also check if image analysis was requested and didn't succeed/get analyzed
                 img_req = results.get("query_details", {}).get("photo_provided", False)
                 img_status = results.get("image_analysis_result", {}).get("status", "not_run")
                 if img_req and img_status in ["analyzed", "failed_analysis"]:
                      all_platforms_failed_or_skipped = False # Image analysis ran partially/fully, not a complete failure

        if all_platforms_failed_or_skipped:
             overall_status = "failed_completely"
             # If no platforms were requested and no photo was provided, maybe it's just skipped?
             if not requested_platforms and not results.get("query_details", {}).get("photo_provided", False):
                  overall_status = "skipped_all"


        return {
            "overall_status": overall_status,
            "total_platform_matches_found": total_matches,
            "platforms_summary": platform_summary,
            "image_analysis_summary": image_summary,
            "errors_encountered": errors if errors else None
        }

    async def full_analysis(self, name: str, photo_path: Optional[Path], platforms_to_search: List[str], query_id: str) -> Dict:
        """
        Performs a full analysis workflow: searches platforms and analyzes image.

        Args:
            name: The name to search for.
            photo_path: Optional path to a photo file for image analysis.
            platforms_to_search: List of platform names (strings) to search on.
            query_id: The unique ID for this query.

        Returns:
            A dictionary containing the analysis results and summary.
        """
        start_time = datetime.now()
        log = logger.bind(query_id=query_id, name=name, photo_path=str(photo_path) if photo_path else "None", platforms=platforms_to_search)
        log.info("Starting full analysis workflow within mapper.")

        # Initialize a results structure specific to this run of full_analysis
        # This will be returned and will overwrite the 'results' variable in main
        run_results: Dict[str, Any] = {
            "query_id": query_id,
            "query_details": {
                "name": name,
                "photo_path": str(photo_path) if photo_path else None,
                 "photo_provided": photo_path is not None,
                "platforms_requested": platforms_to_search,
                "timestamp_utc": start_time.isoformat(),
            },
            "platform_results": {}, # Results from each platform search
            "image_analysis_result": {}, # Result from image analysis
            "summary": {"overall_status": "pending", "errors_encountered": []} # Initial pending state
        }

        # Audit log at the very beginning of the workflow
        if self.security_config.audit_logging:
            await self.log_audit_event("FullAnalysisStarted", {
                "query_id": query_id, "name": name, "photo_provided": photo_path is not None, "platforms_requested": platforms_to_search
            })


        # --- Step 1: Perform Platform Searches ---
        search_tasks = []
        # Filter platforms to search based on which are requested and enabled
        # The list platforms_to_search passed to this method should already be filtered/validated by main
        actual_platforms_to_query = platforms_to_search # Use the validated list from main

        # Note: Skipped platforms (not enabled/configured) were handled in main before calling this.
        # But we should handle potential errors if somehow an unhandled platform gets here.
        for platform_str_raw in actual_platforms_to_query:
             platform_str = platform_str_raw.lower()
             if platform_str in self.integrators:
                 task = asyncio.create_task(
                     self._search_platform(platform_str, name, query_id),
                     name=f"search-{platform_str}-{query_id}"
                 )
                 search_tasks.append((platform_str, task)) # Store platform name with task
                 run_results["platform_results"][platform_str] = {"status": "pending"} # Placeholder
             else:
                 # This case should ideally not happen if main filters correctly, but handle defensively
                 error_msg = f"Integrator not available for requested platform '{platform_str}'. Skipping."
                 log.error(error_msg)
                 run_results["platform_results"][platform_str] = {"status": "error", "error": error_msg, "matches":[]}
                 run_results["summary"]["errors_encountered"].append(error_msg)
                 if self.security_config.audit_logging:
                     await self.log_audit_event("PlatformSearchSkipped", {"query_id": query_id, "platform": platform_str, "reason": "Not configured or enabled (internal check)"})


        if search_tasks:
            log.info(f"Dispatching search tasks for platforms: {[p for p, _ in search_tasks]}")
            # Use a dictionary comprehension to preserve platform names when awaiting
            task_results = await asyncio.gather(*[task for _, task in search_tasks], return_exceptions=True)

            for i, (platform, _) in enumerate(search_tasks):
                result_or_exc = task_results[i]
                if isinstance(result_or_exc, Exception):
                    # An unexpected error occurred during the task execution itself (e.g., CancelledError, unrelated bug)
                    error_msg = f"Platform search task failed unexpectedly for {platform}: {str(result_or_exc)}"
                    log.error(error_msg, exc_info=result_or_exc)
                    run_results["platform_results"][platform] = {"status": "error", "matches": [], "error": error_msg}
                    run_results["summary"]["errors_encountered"].append(error_msg)
                else:
                    # The result from _search_platform (which already handles API errors)
                    run_results["platform_results"][platform] = result_or_exc
                    # If _search_platform returned an error, add it to the main summary errors
                    if result_or_exc.get("status") == "error":
                         run_results["summary"]["errors_encountered"].append(f"Platform '{platform}': {result_or_exc.get('error', 'Unknown error')}")
        else:
            log.warning("No platforms were available/requested for search.")
            # If no platforms were even attempted, make sure the summary reflects this
            if not run_results["summary"].get("errors_encountered"): # Avoid overwriting existing errors
                 run_results["summary"]["errors_encountered"].append("No platforms were available/requested for search.")


        # --- Step 2: Perform Image Analysis (if photo provided and FR enabled) ---
        # Note: Photo path validation and initial skip handling are done in main.
        # photo_path passed here is either None or a validated Path.
        if photo_path is not None and self.image_analyzer.analysis_config.enable_facial_recognition and FACE_RECOGNITION_AVAILABLE:
             log.info("Dispatching image analysis task.")
             # Pass the platform results collected so far for profile pic comparison
             image_analysis_task = asyncio.create_task(
                 self._analyze_image(photo_path, run_results["platform_results"], query_id),
                 name=f"image-analysis-{query_id}"
             )
             try:
                 run_results["image_analysis_result"] = await image_analysis_task
                 # If _analyze_image returned an error/failed_analysis, add it to the main summary errors
                 img_status = run_results["image_analysis_result"].get("status")
                 if img_status in ["error", "failed_analysis"]:
                      run_results["summary"]["errors_encountered"].append(f"Image Analysis ({img_status}): {run_results['image_analysis_result'].get('error', 'Unknown error')}")

             except Exception as img_exc:
                 # An unexpected error during the image analysis task execution itself
                 error_msg = f"Image analysis task failed unexpectedly: {str(img_exc)}"
                 log.error(error_msg, exc_info=img_exc)
                 run_results["image_analysis_result"] = {"status": "error", "error": error_msg}
                 run_results["summary"]["errors_encountered"].append(error_msg)
        else:
             # Image analysis was skipped (handled in main or because FR is off/deps missing)
             # If it was skipped in main, run_results["image_analysis_result"] might already have status:"skipped"
             if "image_analysis_result" not in run_results:
                  skip_reason = "No photo provided." if photo_path is None else "Facial recognition disabled or dependencies missing."
                  run_results["image_analysis_result"] = {"status": "skipped", "reason": skip_reason}
                  # Don't add to errors_encountered here if it's a clean skip based on config/input


        # --- Step 3: Generate Summary and Finalize ---
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        run_results["analysis_duration_seconds"] = round(duration, 3)

        # Generate the final summary based on the collected results
        run_results["summary"].update(self._generate_summary(run_results)) # Update the existing summary dict

        log.info("Full analysis workflow completed.", duration=run_results["analysis_duration_seconds"], summary_status=run_results["summary"]["overall_status"])

        # Audit log at the very end of the workflow
        if self.security_config.audit_logging:
            # Create a summary for the audit log that doesn't include verbose details like matches
            audit_summary = {
                "overall_status": run_results["summary"]["overall_status"],
                "total_platform_matches_found": run_results["summary"].get("total_platform_matches_found", 0),
                "platforms_summary_status": {p: s.get("status") for p, s in run_results["summary"].get("platforms_summary", {}).items()},
                "image_analysis_summary_status": run_results["summary"].get("image_analysis_summary", {}).get("status"),
                "image_face_match_count": run_results["summary"].get("image_analysis_summary", {}).get("face_match_count", 0),
                "errors_count": len(run_results["summary"].get("errors_encountered") or [])
            }
            await self.log_audit_event("FullAnalysisCompleted", {
                "query_id": query_id,
                "duration_seconds": run_results["analysis_duration_seconds"],
                "summary": audit_summary
            })

        return run_results # Return the completed results dictionary


# Configuration Models (repeated here for clarity as they are used by SocialMapperConfig)
class PlatformConfig(BaseModel):
    model_config = ConfigDict(extra='forbid') # Forbid extra fields

    enabled: bool = False
    api_endpoint: Optional[str] = None
    api_type: Optional[str] = None # e.g., 'rapidapi', 'official_api'
    rapidapi_host: Optional[str] = None
    rapidapi_key: Optional[SecretStr] = None # Use SecretStr for sensitive keys
    default_region: Optional[str] = None # Example platform-specific config
    default_count: Optional[int] = Field(None, ge=1) # Example platform-specific config
    default_secUid: Optional[str] = None # Example TikTok specific config

    @field_validator('api_endpoint', 'rapidapi_host', 'rapidapi_key', mode='before')
    @classmethod
    def check_required_for_enabled_rapidapi(cls, v, info):
        """Validate presence of RapidAPI keys if platform is enabled and api_type is rapidapi."""
        data = info.data
        if data.get('enabled') and data.get('api_type') == 'rapidapi':
            if info.field_name in ['rapidapi_host', 'rapidapi_key', 'api_endpoint']:
                if v is None or (isinstance(v, str) and not v.strip()):
                    raise ValueError(f"'{info.field_name}' is required and cannot be empty for enabled RapidAPI integration")

        # Convert str input to SecretStr for rapidapi_key
        if info.field_name == 'rapidapi_key' and v is not None and not isinstance(v, SecretStr):
            return SecretStr(str(v))
        return v

class DatabaseConfig(BaseModel):
    model_config = ConfigDict(extra='forbid')
    dsn: str # Database connection string (e.g., postgresql+asyncpg://...)

class SearchConfig(BaseModel):
    model_config = ConfigDict(extra='forbid')
    default_name: str = Field("John Doe", min_length=1)
    photo_path: Optional[str] = None # Path string or None
    platforms_to_search: List[str] = Field(default_factory=list) # List of platform names (strings)

    @field_validator('platforms_to_search')
    @classmethod
    def convert_to_lower(cls, v):
        """Convert platform names to lowercase for consistent internal handling."""
        return [p.lower() for p in v]


class SocialMapperConfig(BaseModel):
    model_config = ConfigDict(extra='forbid')

    security: SecurityConfig
    database: DatabaseConfig
    platforms: Dict[PlatformType, PlatformConfig] # Use the Enum as key
    image_analysis: ImageAnalysisConfig = Field(default_factory=ImageAnalysisConfig)
    search: SearchConfig = Field(default_factory=SearchConfig)

    @field_validator('platforms')
    @classmethod
    def check_at_least_one_platform_configured(cls, v):
        """Warn if no platforms are configured at all."""
        if not v:
             logger.warning("Configuration validation: 'platforms' section is empty.")
        return v

    # Note: Checking if at least one platform is *enabled* is better done
    # after loading, within the SocialMapperPro class, as the config
    # object itself might be valid even if all are disabled.

# Main Function
async def main():
    """Main function to load config, initialize app, run analysis, and shutdown."""
    config_path = Path("config.json")
    validated_config: Optional[SocialMapperConfig] = None
    mapper: Optional[SocialMapperPro] = None # Keep mapper initialized to None
    query_id = secrets.token_hex(8) # Generate query ID early

    # Initialize results dictionary unconditionally with a default structure
    results: Dict[str, Any] = {
        "query_id": query_id,
        "query_details": {
            "name": "N/A", # Placeholder, will be updated from config
            "photo_path": None,
            "photo_provided": False,
            "platforms_requested": [], # Placeholder, will be updated
            "timestamp_utc": datetime.now().isoformat(),
        },
        "platform_results": {},
        "image_analysis_result": {},
        "summary": {"overall_status": "failed", "errors_encountered": []}, # Default failed state
        "analysis_duration_seconds": 0.0
    }
    log = logger.bind(query_id=query_id) # Bind log early with query_id for initial errors


    # --- Step 0: Configuration Loading and Initial Validation ---
    try:
        logger.info("Loading configuration from '%s'.", config_path) # Use %s for string formatting with structlog
        # Check if default config needs generating
        if not config_path.exists():
            logger.warning(f"Configuration file '{config_path}' not found. Generating a default configuration file.")
            try:
                default_key = base64.urlsafe_b64encode(secrets.token_bytes(32)).decode()
                default_config_content = {
                    "security": {
                        "encryption_key": default_key,
                        "key_rotation_days": 7, # Set to 0 to disable rotation
                        "sanitize_inputs": True,
                        "request_signatures": True,
                        "audit_logging": True,
                        "max_retries": 3,
                        "max_file_size": 10485760 # 10 MB
                    },
                    "database": {
                        # Replace with your actual database DSN.
                        # Example for PostgreSQL using asyncpg: "postgresql+asyncpg://user:pass@localhost:5432/mydatabase"
                        # If not using a real DB, audit logs will be logged to stdout with a warning.
                        "dsn": "sqlite+aiosqlite:///./socialsearch_audit.db" # Example placeholder for a local file DB
                    },
                    "platforms": {
                        # Configure enabled platforms here.
                        # Set "enabled": true for the platforms you want to use.
                        # Fill in API details for enabled platforms.
                        "facebook": {"enabled": False},
                        "linkedin": {"enabled": False},
                        "instagram": {
                            "enabled": True, # Set to true to enable Instagram searches
                            "api_type": "rapidapi",
                            # Replace with the actual RapidAPI endpoint you are using for user search
                            "api_endpoint": "https://rocketapi-for-developers.p.rapidapi.com/instagram/search",
                            "rapidapi_host": "rocketapi-for-developers.p.rapidapi.com",
                            # !!! Replace with your actual RapidAPI key !!!
                            "rapidapi_key": "YOUR_RAPIDAPI_KEY_HERE",
                            "default_count": 20 # Example parameter
                        },
                        "snapchat": {"enabled": False},
                        "tiktok": {"enabled": False} # Set to true and add config if you have a TikTok API
                    },
                    "image_analysis": {
                        "enable_facial_recognition": True, # Set to false to disable FR features
                        "face_detection_model": "hog", # 'hog' is CPU based, 'cnn' is GPU accelerated (if available)
                        "min_confidence_threshold": 0.6 # Lower value matches more faces (less strict)
                    },
                    "search": {
                        # Configure the default search parameters when running the script directly.
                        "default_name": "Virat Kohli", # The name to search for
                        # Optional: Path to a local photo file (relative or absolute).
                        # Set to null/None or provide an existing image path for image analysis.
                        "photo_path": None, # "path/to/your/photo.jpg"
                        # List the platforms you want to search from the configured and enabled ones.
                        # Must match keys in the "platforms" section.
                        "platforms_to_search": ["instagram", "tiktok"] # Example list
                    }
                }
                with open(config_path, "w") as f:
                    json.dump(default_config_content, f, indent=4)
                logger.info(f"Default configuration saved to '{config_path}'. Please update sensitive keys (like 'rapidapi_key') and 'dsn'. Exiting.")
                # No further processing if we just generated config
                results["summary"]["errors_encountered"].append("Default config generated. Please update config.json.")
                return results # Return results indicating config generation

            except Exception as e:
                error_msg = f"Error generating default config file: {e}"
                logger.error(error_msg, exc_info=True)
                results["summary"]["errors_encountered"].append(error_msg)
                return results # Return results indicating generation failure


        # Load existing config
        with open(config_path, "r") as f:
            config_data = json.load(f)

        # Use Pydantic model for loading and validation
        validated_config = SocialMapperConfig(**config_data)
        logger.info("Configuration loaded and validated successfully.")

        # Specific checks for required fields after general validation
        instagram_cfg = validated_config.platforms.get(PlatformType.INSTAGRAM)
        if instagram_cfg and instagram_cfg.enabled and instagram_cfg.api_type == 'rapidapi' and \
           instagram_cfg.rapidapi_key and instagram_cfg.rapidapi_key.get_secret_value() == "YOUR_RAPIDAPI_KEY_HERE":
             error_msg = "Invalid RapidAPI key placeholder found for Instagram. Please update 'platforms.instagram.rapidapi_key' in config.json."
             logger.critical(error_msg) # Use critical as this prevents API calls
             results["summary"]["errors_encountered"].append(error_msg)
             return results # Return results with config error


    except FileNotFoundError:
        # This block is less likely now that we check and generate default config
        error_msg = f"Configuration file '{config_path}' not found."
        logger.critical(error_msg)
        results["summary"]["errors_encountered"].append(error_msg)
        return results # Return results with error
    except json.JSONDecodeError:
        error_msg = f"Invalid JSON format in '{config_path}'. Please check the file syntax."
        logger.critical(error_msg)
        results["summary"]["errors_encountered"].append(error_msg)
        return results # Return results with error
    except Exception as e:
        # Catch any other error during config loading/validation (including Pydantic ValidationErrors)
        error_msg = f"Configuration loading or validation failed: {e}"
        logger.critical(error_msg, exc_info=True)
        results["summary"]["errors_encountered"].append(error_msg)
        return results # Return results with error


    # --- Step 1: Application Initialization and Initial Checks ---
    # This block only runs if config loading/validation was successful
    start_time = datetime.now() # Start timer after config load

    try:
        # Initialize the main application with the validated config
        # This might raise ValueError if config is structurally valid but semantically wrong (less likely after Pydantic)
        mapper = SocialMapperPro(validated_config.model_dump()) # Pass model_dump dictionary

        # Connect to database and schedule tasks
        # This might raise exceptions if DB connection fails, etc.
        await mapper.initialize()

        # Get search parameters from validated config
        search_cfg = validated_config.search
        name_to_search = search_cfg.default_name.strip()
        photo_path_str = search_cfg.photo_path
        platforms_to_query_from_config = search_cfg.platforms_to_search # This list is already lowercased by validator

        # Update query details in the results dictionary
        results["query_details"].update({
            "name": name_to_search,
            "platforms_requested": platforms_to_query_from_config,
            "timestamp_utc": start_time.isoformat(),
        })
        # Re-bind log with more details after getting search params
        log = log.bind(name=name_to_search, platforms=platforms_to_query_from_config)


        # --- Handle search parameter validation and update results ---
        if not name_to_search:
            error_msg = "Search name is empty or whitespace. Aborting analysis."
            log.error(error_msg)
            results["summary"]["errors_encountered"].append(error_msg)
            results["summary"]["overall_status"] = "failed" # Explicitly mark as failed
            # Log audit for failed start (name empty)
            if mapper.security_config.audit_logging:
                await mapper.log_audit_event("FullAnalysisFailed", {"query_id": query_id, "reason": error_msg, "summary": results["summary"]})
            return results # Return results with error


        # Determine which platforms can actually be searched (enabled and requested)
        enabled_platforms = {p.value for p, cfg in validated_config.platforms.items() if cfg.enabled}
        final_platforms_to_search = [p for p in platforms_to_query_from_config if p in enabled_platforms]

        skipped_platforms = [p for p in platforms_to_query_from_config if p not in enabled_platforms]
        if skipped_platforms:
            log.warning(f"Skipping platforms not configured or enabled: {skipped_platforms}")
            for skipped_p in skipped_platforms:
                 results["platform_results"][skipped_p] = {"status": "skipped", "error": f"Platform '{skipped_p}' not configured or enabled.", "matches": []}
                 results["summary"]["errors_encountered"].append(f"Platform '{skipped_p}' skipped: Not configured or enabled.") # Add to errors for summary


        if not final_platforms_to_search:
            error_msg = "No configured and enabled platforms were requested for search."
            log.warning(error_msg)
            results["summary"]["errors_encountered"].append(error_msg)
            results["summary"]["overall_status"] = "failed" # Explicitly mark as failed
            # Log audit for failed start (no platforms)
            if mapper.security_config.audit_logging:
                await mapper.log_audit_event("FullAnalysisFailed", {"query_id": query_id, "reason": error_msg, "summary": results["summary"]})
            return results # Return results with error


        # Resolve photo path if provided and enabled/deps available
        photo_path_resolved: Optional[Path] = None
        image_analysis_skipped_reason = None
        if photo_path_str:
            photo_path_resolved = Path(photo_path_str).resolve()
            if not await asyncio.to_thread(photo_path_resolved.is_file):
                image_analysis_skipped_reason = f"Photo file not found at path '{photo_path_str}'."
                log.warning(f"{image_analysis_skipped_reason} Image analysis will be skipped.", resolved_path=str(photo_path_resolved))
                photo_path_resolved = None
            elif not validated_config.image_analysis.enable_facial_recognition:
                 image_analysis_skipped_reason = "Image analysis is disabled in configuration."
                 log.warning(f"Photo path provided, but {image_analysis_skipped_reason} Image analysis will be skipped.")
                 photo_path_resolved = None
            elif not FACE_RECOGNITION_AVAILABLE:
                 image_analysis_skipped_reason = "`face_recognition` or `Pillow` are not installed."
                 log.warning(f"Photo path provided, but {image_analysis_skipped_reason} Image analysis will be skipped.")
                 photo_path_resolved = None
            else:
                log = log.bind(photo_path=str(photo_path_resolved)) # Add photo path to log context
                logger.info(f"Using photo for analysis: '{photo_path_resolved}'.")

        results["query_details"]["photo_path"] = str(photo_path_resolved) if photo_path_resolved else None
        results["query_details"]["photo_provided"] = photo_path_resolved is not None

        # If image analysis was skipped *due to a condition checked here*, record it in results
        # Note: if photo_path_str was None initially, image_analysis_result starts empty and will be handled in full_analysis
        if photo_path_str and photo_path_resolved is None and image_analysis_skipped_reason:
             results["image_analysis_result"] = {"status": "skipped", "reason": image_analysis_skipped_reason}
             results["summary"]["errors_encountered"].append(f"Image Analysis skipped: {image_analysis_skipped_reason}")

        # If we reached here, we have a valid name and at least one enabled platform to search.
        # We can now call the core full_analysis workflow method on the mapper instance.
        # This method will populate the platform_results and image_analysis_result fields.
        log.info(f"Calling full analysis workflow on mapper for name='{name_to_search}' on platforms: {final_platforms_to_search}",
                    photo_provided=photo_path_resolved is not None)

        # Execute the full analysis workflow using the mapper instance
        # This call *will overwrite* the 'results' variable in this main scope with the full outcome
        results = await mapper.full_analysis(
            name=name_to_search,
            photo_path=photo_path_resolved,
            platforms_to_search=final_platforms_to_search, # Pass the filtered list
            query_id=query_id # Pass the generated query_id
        )

        # --- Output Results ---
        # These lines are now safe because 'results' is guaranteed to have been assigned
        # either the initial default error state (if config/init failed) or the return value from full_analysis.
        # The query_id is also safe as it was initialized early.

        output_path = Path(f"results_{query_id}.json")

        try:
            # Prepare results for file output (remove sensitive/large fields)
            output_results = results.copy()
            if 'image_analysis_result' in output_results:
                 output_results['image_analysis_result'].pop('face_encodings', None)
                 output_results['image_analysis_result'].pop('face_locations', None)
                 # Also sanitize matches within image analysis results for file
                 image_analysis_output_sanitized_matches = mapper._sanitize_audit_details(output_results['image_analysis_result'].get('profile_pic_matches', []))
                 output_results['image_analysis_result']['profile_pic_matches'] = image_analysis_output_sanitized_matches

            # Sanitize platform matches for file
            # Decide if you want to keep original or replace with sanitized
            # Let's replace for safety in the default file output
            output_results['platform_results_sanitized'] = {} # Create a new key for sanitized platform matches
            for platform, p_result in output_results.get("platform_results", {}).items():
                 output_results['platform_results_sanitized'][platform] = {
                     "status": p_result.get("status"),
                     "error": p_result.get("error"),
                     "matches": mapper._sanitize_audit_details(p_result.get("matches", [])) # Sanitize the list of matches
                 }
            output_results.pop('platform_results', None) # Remove the original unsanitized list

            try:
                with open(output_path, "w") as f:
                    json.dump(output_results, f, indent=2)
                logger.info(f"Full results saved to '{output_path}'.")
            except FileNotFoundError:
                # Try saving to the user's home directory as a fallback
                home_dir = os.path.expanduser("~")
                alt_output_path = os.path.join(home_dir, f"results_{query_id}.json")
                with open(alt_output_path, "w") as f:
                    json.dump(output_results, f, indent=2)
                logger.info(f"Full results saved to alternative location: '{alt_output_path}'.")

            # Print a formatted summary to console
            print("\n" + "="*30 + f" Analysis Results ({query_id}) " + "="*30)
            print(json.dumps(results.get("summary", {"message": "Summary not available."}), indent=2)) # Print summary

            # Print Platform Matches (sanitized for console)
            print("\n" + "="*30 + " Platform Matches " + "="*30)
            formatted_platform_matches = {}
            # Use the original results for console printing, applying sanitization on the fly
            for platform, p_result in results.get("platform_results", {}).items():
                 formatted_platform_matches[platform] = {
                     "status": p_result.get("status"),
                     "error": p_result.get("error"),
                     # Use sanitization logic for console print too
                     "matches": mapper._sanitize_audit_details(p_result.get("matches", []))
                 }
            print(json.dumps(formatted_platform_matches, indent=2))


            # === Specific Section: Instagram Usernames and URLs ===
            print("\n" + "="*30 + " Instagram Profiles Found " + "="*30)
            instagram_results = results.get("platform_results", {}).get("instagram")

            if instagram_results:
                if instagram_results.get("status") == "completed":
                    instagram_matches = instagram_results.get("matches", [])
                    if instagram_matches:
                        # Prepare a list of dictionaries with only username and profile_url
                        profile_list = []
                        for match in instagram_matches:
                            username = match.get("username")
                            profile_url = match.get("profile_url")
                            full_name = match.get("name", "")
                            is_verified = match.get("is_verified", False)
                            followers = match.get("followers", 0)

                            # Only add if username is present
                            if username:
                                profile_list.append({
                                    "username": username,
                                    "full_name": full_name,
                                    "profile_url": profile_url,
                                    "is_verified": is_verified,
                                    "followers": followers
                                })

                        if profile_list:
                            print(f"Found {len(profile_list)} Instagram profile(s):")
                            print(json.dumps(profile_list, indent=2))
                        else:
                            print("No Instagram profiles found with valid usernames in the results.")
                    else:
                        print("Instagram search completed successfully but found 0 matching profiles.")
                        print("This could be due to:")
                        print("- No users matching the search term")
                        print("- Search filters being too strict (try verified accounts or accounts with more followers)")
                        print("- Spelling variations in the search term")
                elif instagram_results.get("status") == "skipped":
                    print("Instagram search was skipped based on configuration.")
                elif instagram_results.get("status") == "error":
                    print(f"Instagram search encountered an error: {instagram_results.get('error', 'Unknown error')}")
                else:
                    print(f"Instagram search status: {instagram_results.get('status', 'unknown')}")
            else:
                # This covers cases where 'instagram' key might not even be in platform_results (e.g., not requested)
                print("Instagram search was not requested or results are not available.")

            # === END Specific Section ===


            # Print Image Analysis Details (sanitized for console)
            print("\n" + "="*30 + " Image Analysis Details " + "="*30)
            image_analysis_output = results.get("image_analysis_result", {})
            # Explicitly remove potentially large/sensitive fields before printing console details
            image_analysis_output_for_print = image_analysis_output.copy()
            image_analysis_output_for_print.pop('face_encodings', None)
            image_analysis_output_for_print.pop('face_locations', None)
            # Sanitize profile match details just in case sensitive info leaked (though they shouldn't have)
            image_analysis_output_for_print['profile_pic_matches'] = mapper._sanitize_audit_details(image_analysis_output_for_print.get('profile_pic_matches', []))

            print(json.dumps(image_analysis_output_for_print, indent=2))

            print("="*80 + "\n")

        except Exception as e:
             logger.error(f"Failed to save or print results: {e}", exc_info=True)


    except Exception as e:
        # This block catches any unhandled exception that occurs *after* the mapper
        # object has been successfully initialized and before the final print/save block.
        error_msg = f"A critical error occurred during application execution: {e}"
        logger.critical(error_msg, exc_info=True)
        # Update the results dictionary with the critical error before finally block
        results["summary"]["errors_encountered"].append(error_msg)
        results["summary"]["overall_status"] = results["summary"].get("overall_status", "failed_partially") # Don't overwrite if already marked failed
        if results["summary"]["overall_status"] not in ["failed", "failed_completely", "failed_partially"]:
             results["summary"]["overall_status"] = "failed_partially"


    finally:
        # Ensure graceful shutdown even if errors occur
        # Only shut down mapper if it was successfully initialized
        if mapper:
            await mapper.shutdown()

        logger.info("Application finished.")
        # You could add a final print of the summary here if you want to
        # guarantee it always shows, even if printing results inside the try fails.


if __name__ == "__main__":
    # Initial basic logging setup before structlog configures stdlib logger
    # This ensures messages before structlog is fully set up are visible
    # structlog configuration above replaces the standard formatter/handler
    # The level can be controlled via environment variables or structlog configuration if needed.
    # For simple scripts, basicConfig is okay, but structlog's setup is more powerful.
    # The current structlog config effectively directs output to the standard logging stream, which basicConfig *could* format.
    # However, the structlog.stdlib processors are designed to output structured logs, often best consumed raw or by a specific formatter.
    # ThebasicConfig line with %(name)s should make it clearer which logger is sending the message.
    logger.info("Starting SocialMapperPro application.")
    asyncio.run(main())
