#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Social Search Launcher
This script provides a simple interface to run the social search functionality.
"""

import os
import sys
import json
import subprocess

def print_header():
    """Print a nice header for the application."""
    print("=" * 60)
    print("Social Search Pro - Unified Enterprise OSINT Solution".center(60))
    print("=" * 60)
    print()

def check_config():
    """Check if the config file exists and is valid."""
    if not os.path.exists("config.json"):
        print("Error: config.json not found!")
        print("Please create a config.json file before running the application.")
        return False
    
    try:
        with open("config.json", "r") as f:
            json.load(f)
        return True
    except json.JSONDecodeError:
        print("Error: config.json contains invalid JSON!")
        print("Please check the file format and try again.")
        return False

def run_social_search():
    """Run the social search script."""
    print("Starting social search...")
    print()
    
    try:
        subprocess.run([sys.executable, "social_search_result.py"], check=True)
        print()
        print("Social search completed successfully!")
    except subprocess.CalledProcessError:
        print()
        print("Error: Social search failed to run correctly.")
        print("Check the output above for more details.")
    except FileNotFoundError:
        print()
        print("Error: social_search1.py not found!")
        print("Make sure the script is in the current directory.")

def main():
    """Main function to run the application."""
    print_header()
    
    if not check_config():
        return
    
    run_social_search()

if __name__ == "__main__":
    main()
