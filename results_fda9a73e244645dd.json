{"query_id": "fda9a73e244645dd", "query_details": {"name": "cristano", "photo_path": "C:\\Users\\<USER>\\Desktop\\Social search API\\profile.jpg", "photo_provided": true, "platforms_requested": ["instagram"], "timestamp_utc": "2025-07-24T18:31:53.257346"}, "image_analysis_result": {"status": "analyzed", "file_path": "C:\\Users\\<USER>\\Desktop\\Social search API\\profile.jpg", "file_type": "image/jpeg", "size_bytes": 18652, "error": null, "facial_recognition_enabled_in_config": true, "facial_recognition_actually_run": true, "face_count": 1, "profile_pic_matches": []}, "summary": {"overall_status": "completed", "errors_encountered": null, "total_platform_matches_found": 0, "platforms_summary": {"instagram": {"status": "completed", "match_count": 0, "error": null}}, "image_analysis_summary": {"status": "analyzed", "face_count": 1, "face_match_count": 0, "error": null}}, "analysis_duration_seconds": 4.759, "platform_results_sanitized": {"instagram": {"status": "completed", "error": null, "matches": []}}}